using Microsoft.EntityFrameworkCore;
using NafaPlace.Inventory.Application.DTOs;
using NafaPlace.Inventory.Domain.Models;
using NafaPlace.Inventory.Infrastructure.Data;

namespace NafaPlace.Inventory.Infrastructure.Repositories;

public class InventoryRepository : IInventoryRepository
{
    private readonly InventoryDbContext _context;

    public InventoryRepository(InventoryDbContext context)
    {
        _context = context;
    }

    // Stock Reservations
    public async Task<StockReservation> CreateReservationAsync(StockReservation reservation)
    {
        _context.StockReservations.Add(reservation);
        await _context.SaveChangesAsync();
        return reservation;
    }

    public async Task<bool> UpdateReservationAsync(StockReservation reservation)
    {
        _context.StockReservations.Update(reservation);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<StockReservation?> GetReservationAsync(int reservationId)
    {
        return await _context.StockReservations
            .FirstOrDefaultAsync(r => r.Id == reservationId);
    }

    public async Task<List<StockReservation>> GetUserReservationsAsync(string userId)
    {
        return await _context.StockReservations
            .Where(r => r.UserId == userId)
            .OrderByDescending(r => r.ReservedAt)
            .ToListAsync();
    }

    public async Task<List<StockReservation>> GetProductReservationsAsync(int productId)
    {
        return await _context.StockReservations
            .Where(r => r.ProductId == productId && r.Status == ReservationStatus.Active)
            .OrderByDescending(r => r.ReservedAt)
            .ToListAsync();
    }

    public async Task<List<StockReservation>> GetExpiredReservationsAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.StockReservations
            .Where(r => r.Status == ReservationStatus.Active && r.ExpiresAt < now)
            .ToListAsync();
    }

    public async Task<int> GetReservedStockAsync(int productId)
    {
        var now = DateTime.UtcNow;
        return await _context.StockReservations
            .Where(r => r.ProductId == productId && 
                       r.Status == ReservationStatus.Active && 
                       r.ExpiresAt > now)
            .SumAsync(r => r.Quantity);
    }

    public async Task<int> GetActiveReservationsCountAsync(int? sellerId = null)
    {
        var query = _context.StockReservations
            .Where(r => r.Status == ReservationStatus.Active);

        if (sellerId.HasValue)
        {
            // Ici on devrait joindre avec la table des produits pour filtrer par vendeur
            // Pour l'instant, on retourne le count total
        }

        return await query.CountAsync();
    }

    public async Task<int> CleanupExpiredReservationsAsync()
    {
        var expiredReservations = await GetExpiredReservationsAsync();
        
        foreach (var reservation in expiredReservations)
        {
            reservation.Status = ReservationStatus.Expired;
            reservation.ReleasedAt = DateTime.UtcNow;
        }

        if (expiredReservations.Any())
        {
            await _context.SaveChangesAsync();
        }

        return expiredReservations.Count;
    }

    // Stock Alerts
    public async Task<StockAlert> CreateAlertAsync(StockAlert alert)
    {
        _context.StockAlerts.Add(alert);
        await _context.SaveChangesAsync();
        return alert;
    }

    public async Task<bool> UpdateAlertAsync(StockAlert alert)
    {
        _context.StockAlerts.Update(alert);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<StockAlert?> GetAlertAsync(int alertId)
    {
        return await _context.StockAlerts
            .Include(a => a.Notifications)
            .FirstOrDefaultAsync(a => a.Id == alertId);
    }

    public async Task<List<StockAlert>> GetActiveAlertsAsync(int? sellerId = null)
    {
        var query = _context.StockAlerts
            .Where(a => a.IsActive);

        if (sellerId.HasValue)
        {
            query = query.Where(a => a.SellerId == sellerId.Value);
        }

        return await query
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<StockAlert>> GetSellerAlertsAsync(int sellerId)
    {
        return await _context.StockAlerts
            .Where(a => a.SellerId == sellerId)
            .OrderByDescending(a => a.CreatedAt)
            .ToListAsync();
    }

    public async Task<List<StockAlert>> GetPendingAlertsAsync()
    {
        return await _context.StockAlerts
            .Where(a => a.IsActive && !a.IsAcknowledged)
            .OrderBy(a => a.Severity)
            .ThenByDescending(a => a.CreatedAt)
            .ToListAsync();
    }

    public async Task<int> GetPendingAlertsCountAsync(int? sellerId = null)
    {
        var query = _context.StockAlerts
            .Where(a => a.IsActive && !a.IsAcknowledged);

        if (sellerId.HasValue)
        {
            query = query.Where(a => a.SellerId == sellerId.Value);
        }

        return await query.CountAsync();
    }

    // Stock Movements
    public async Task<StockMovement> CreateMovementAsync(StockMovement movement)
    {
        _context.StockMovements.Add(movement);
        await _context.SaveChangesAsync();
        return movement;
    }

    public async Task<List<StockMovement>> GetProductMovementsAsync(int productId, int page = 1, int pageSize = 20)
    {
        return await _context.StockMovements
            .Where(m => m.ProductId == productId)
            .OrderByDescending(m => m.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<List<StockMovement>> GetSellerMovementsAsync(int sellerId, int page = 1, int pageSize = 20)
    {
        return await _context.StockMovements
            .Where(m => m.SellerId == sellerId)
            .OrderByDescending(m => m.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<int> CleanupOldMovementsAsync(int daysToKeep = 90)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
        var oldMovements = await _context.StockMovements
            .Where(m => m.CreatedAt < cutoffDate)
            .ToListAsync();

        if (oldMovements.Any())
        {
            _context.StockMovements.RemoveRange(oldMovements);
            await _context.SaveChangesAsync();
        }

        return oldMovements.Count;
    }

    // Analytics
    public async Task<List<TopProductDto>> GetTopSellingProductsAsync(int? sellerId = null, int count = 10)
    {
        var query = _context.StockMovements
            .Where(m => m.Type == MovementType.Sale);

        if (sellerId.HasValue)
        {
            query = query.Where(m => m.SellerId == sellerId.Value);
        }

        var topProducts = await query
            .GroupBy(m => new { m.ProductId, m.ProductName })
            .Select(g => new TopProductDto
            {
                ProductId = g.Key.ProductId,
                ProductName = g.Key.ProductName,
                SoldQuantity = g.Sum(m => m.Quantity),
                Revenue = 0, // Serait calculé avec les prix
                Currency = "GNF"
            })
            .OrderByDescending(p => p.SoldQuantity)
            .Take(count)
            .ToListAsync();

        return topProducts;
    }

    public async Task<List<TopProductDto>> GetLowStockProductsAsync(int? sellerId = null, int threshold = 10)
    {
        // Cette méthode nécessiterait une jointure avec la table des produits
        // Pour l'instant, on retourne une liste vide
        return await Task.FromResult(new List<TopProductDto>());
    }
}
