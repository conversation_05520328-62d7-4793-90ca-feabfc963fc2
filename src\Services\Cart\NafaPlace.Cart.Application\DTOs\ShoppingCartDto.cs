namespace NafaPlace.Cart.Application.DTOs;

public class ShoppingCartDto
{
    public string UserId { get; set; } = string.Empty;
    public List<CartItemDto> Items { get; set; } = new();
    public int ItemCount { get; set; }
    public decimal SubTotal { get; set; }
    public decimal ShippingFee { get; set; }
    public decimal Tax { get; set; }
    public decimal Total { get; set; }
    
    // Coupon information
    public string? CouponCode { get; set; }
    public decimal CouponDiscount { get; set; } = 0;
    public string? CouponDescription { get; set; }
    
    public DateTime LastUpdated { get; set; }
}

public class CartItemDto
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal UnitPrice { get; set; }
    public int Quantity { get; set; }
    public string Currency { get; set; } = "GNF";
    public string? ProductImageUrl { get; set; }
    public decimal LineTotal => UnitPrice * Quantity;
}

public class CartItemCreateDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public int Quantity { get; set; } = 1;
    public string Currency { get; set; } = "GNF";
    public string? ProductImageUrl { get; set; }
}

public class CartItemUpdateDto
{
    public int Quantity { get; set; }
}

public class ApplyCouponRequest
{
    public string CouponCode { get; set; } = string.Empty;
}
