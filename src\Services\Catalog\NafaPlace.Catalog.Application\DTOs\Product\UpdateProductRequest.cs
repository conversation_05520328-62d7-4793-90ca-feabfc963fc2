using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace NafaPlace.Catalog.Application.DTOs.Product;

public class UpdateProductRequest
{
    public required string Name { get; set; }

    public required string Description { get; set; }

    public decimal Price { get; set; }

    public required string Currency { get; set; }

    public int StockQuantity { get; set; }

    public int? CategoryId { get; set; }

    public required string Brand { get; set; }

    public required string Model { get; set; }

    public decimal Weight { get; set; }

    public required string Dimensions { get; set; }

    public bool IsActive { get; set; }
    public bool IsFeatured { get; set; }

    public List<UpdateProductImageRequest> Images { get; set; } = new();
    public List<UpdateProductVariantRequest> Variants { get; set; } = new();
    public List<UpdateProductAttributeRequest> Attributes { get; set; } = new();
}

