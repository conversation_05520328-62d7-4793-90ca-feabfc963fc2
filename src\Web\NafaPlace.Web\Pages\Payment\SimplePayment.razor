@page "/payment/simple/{OrderId:int}"
@using NafaPlace.Web.Models.Order
@using NafaPlace.Web.Services
@inject NavigationManager NavigationManager
@inject IOrderService OrderService

<PageTitle>Paiement - NafaPlace</PageTitle>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            @if (_loading)
            {
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2">Chargement des détails de la commande...</p>
                </div>
            }
            else if (_order == null)
            {
                <div class="alert alert-danger">
                    <h4>Commande introuvable</h4>
                    <p>La commande demandée n'existe pas ou n'est plus disponible.</p>
                    <a href="/" class="btn btn-primary">Retour à l'accueil</a>
                </div>
            }
            else
            {
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-credit-card me-2"></i>
                            Finaliser le paiement
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- Résumé de la commande -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>Détails de la commande</h5>
                                <p><strong>Numéro:</strong> #@_order.Id.ToString().PadLeft(8, '0')</p>
                                <p><strong>Date:</strong> @_order.OrderDate.ToString("dd/MM/yyyy HH:mm")</p>
                                <p><strong>Statut:</strong> 
                                    <span class="badge bg-info">@_order.Status</span>
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h5>Montant à payer</h5>
                                <div class="bg-light p-3 rounded">
                                    <div class="d-flex justify-content-between">
                                        <span>Sous-total:</span>
                                        <span>@_order.TotalAmount.ToString("N0") GNF</span>
                                    </div>
                                    <div class="d-flex justify-content-between">
                                        <span>Livraison:</span>
                                        <span>Gratuite</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between fw-bold">
                                        <span>Total:</span>
                                        <span class="text-primary">@_order.TotalAmount.ToString("N0") GNF</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Options de paiement -->
                        @if (_order.PaymentStatus != "Completed")
                        {
                            <h5 class="mb-3">Choisissez votre mode de paiement</h5>
                            
                            <div class="row g-3">
                                <!-- Stripe -->
                                <div class="col-md-4">
                                    <div class="card h-100 payment-option">
                                        <div class="card-body text-center">
                                            <i class="fab fa-stripe fa-3x text-primary mb-3"></i>
                                            <h6>Carte bancaire</h6>
                                            <p class="small text-muted">Paiement sécurisé par Stripe</p>
                                            <button class="btn btn-primary w-100" 
                                                    @onclick="@(() => ProcessPayment("Stripe"))"
                                                    disabled="@(_processing && _selectedMethod == "Stripe")">
                                                @if (_processing && _selectedMethod == "Stripe")
                                                {
                                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                                }
                                                Payer par carte
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Orange Money -->
                                <div class="col-md-4">
                                    <div class="card h-100 payment-option">
                                        <div class="card-body text-center">
                                            <i class="fas fa-mobile-alt fa-3x text-warning mb-3"></i>
                                            <h6>Orange Money</h6>
                                            <p class="small text-muted">Paiement mobile sécurisé</p>
                                            <button class="btn btn-warning w-100" 
                                                    @onclick="@(() => ProcessPayment("OrangeMoney"))"
                                                    disabled="@(_processing && _selectedMethod == "OrangeMoney")">
                                                @if (_processing && _selectedMethod == "OrangeMoney")
                                                {
                                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                                }
                                                Payer avec Orange Money
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Cash on Delivery -->
                                <div class="col-md-4">
                                    <div class="card h-100 payment-option">
                                        <div class="card-body text-center">
                                            <i class="fas fa-money-bill-wave fa-3x text-success mb-3"></i>
                                            <h6>Paiement à la livraison</h6>
                                            <p class="small text-muted">Payez en espèces à la réception</p>
                                            <button class="btn btn-success w-100" 
                                                    @onclick="@(() => ProcessPayment("CashOnDelivery"))"
                                                    disabled="@(_processing && _selectedMethod == "CashOnDelivery")">
                                                @if (_processing && _selectedMethod == "CashOnDelivery")
                                                {
                                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                                }
                                                Confirmer
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Paiement confirmé</h5>
                                <p class="mb-0">Votre paiement a été traité avec succès. Votre commande est en cours de préparation.</p>
                            </div>
                        }

                        <!-- Actions -->
                        <div class="mt-4 text-center">
                            <a href="/account/orders" class="btn btn-outline-secondary me-2">
                                <i class="fas fa-list me-2"></i>Mes commandes
                            </a>
                            <a href="/" class="btn btn-outline-primary">
                                <i class="fas fa-home me-2"></i>Continuer mes achats
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public int OrderId { get; set; }

    private OrderDto? _order;
    private bool _loading = true;
    private bool _processing = false;
    private string _selectedMethod = "";

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _order = await OrderService.GetOrderByIdAsync(OrderId);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de la commande: {ex.Message}");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task ProcessPayment(string method)
    {
        if (_processing) return;

        _processing = true;
        _selectedMethod = method;

        try
        {
            switch (method)
            {
                case "Stripe":
                    NavigationManager.NavigateTo($"/payment/stripe-form/{OrderId}");
                    break;
                case "OrangeMoney":
                    await ProcessOrangeMoneyPayment();
                    break;
                case "CashOnDelivery":
                    await ProcessCashOnDeliveryPayment();
                    break;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du traitement du paiement: {ex.Message}");
        }
        finally
        {
            _processing = false;
            _selectedMethod = "";
        }
    }

    private async Task ProcessOrangeMoneyPayment()
    {
        try
        {
            await OrderService.UpdatePaymentStatusAsync(OrderId, "Completed", "OM-" + Guid.NewGuid().ToString("N")[..8]);
            NavigationManager.NavigateTo("/payment/success");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur Orange Money: {ex.Message}");
        }
    }

    private async Task ProcessCashOnDeliveryPayment()
    {
        try
        {
            await OrderService.UpdatePaymentStatusAsync(OrderId, "Pending", "COD-" + Guid.NewGuid().ToString("N")[..8]);
            NavigationManager.NavigateTo("/payment/success");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur paiement à la livraison: {ex.Message}");
        }
    }

    private string GetPaymentStatusText(string status)
    {
        return status switch
        {
            "Pending" => "En attente",
            "Completed" => "Payé",
            "Failed" => "Échec",
            _ => "Non défini"
        };
    }
}
