using System.ComponentModel.DataAnnotations;
using NafaPlace.Common.Models;

namespace NafaPlace.Coupon.Domain.Models;

public class Coupon : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public required string Code { get; set; }

    [Required]
    [MaxLength(200)]
    public required string Name { get; set; }

    [MaxLength(500)]
    public string? Description { get; set; }

    [Required]
    public CouponType Type { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal Value { get; set; }

    [Range(0, double.MaxValue)]
    public decimal? MinimumOrderAmount { get; set; }

    [Range(0, double.MaxValue)]
    public decimal? MaximumDiscountAmount { get; set; }

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    [Range(0, int.MaxValue)]
    public int? UsageLimit { get; set; }

    [Range(0, int.MaxValue)]
    public int? UsageLimitPerUser { get; set; }

    public int UsageCount { get; set; } = 0;

    public bool IsActive { get; set; } = true;

    [Required]
    [MaxLength(3)]
    public required string Currency { get; set; } = "GNF";

    // Conditions d'application
    public bool ApplicableToAllProducts { get; set; } = true;
    public List<int>? ApplicableProductIds { get; set; }
    public List<int>? ApplicableCategoryIds { get; set; }
    public List<int>? ApplicableSellerIds { get; set; }

    // Exclusions
    public List<int>? ExcludedProductIds { get; set; }
    public List<int>? ExcludedCategoryIds { get; set; }
    public List<int>? ExcludedSellerIds { get; set; }

    // Métadonnées
    [MaxLength(50)]
    public string? CreatedBy { get; set; }

    [MaxLength(50)]
    public string? UpdatedBy { get; set; }

    // Navigation properties
    public virtual ICollection<CouponUsage> Usages { get; set; } = new List<CouponUsage>();

    // Computed properties
    public bool IsExpired => DateTime.UtcNow > EndDate;
    public bool IsNotStarted => DateTime.UtcNow < StartDate;
    public bool IsUsageLimitReached => UsageLimit.HasValue && UsageCount >= UsageLimit.Value;
    public bool IsValid => IsActive && !IsExpired && !IsNotStarted && !IsUsageLimitReached;
}

public enum CouponType
{
    FixedAmount = 1,    // Montant fixe (ex: 10,000 GNF de réduction)
    Percentage = 2,     // Pourcentage (ex: 15% de réduction)
    FreeShipping = 3,   // Livraison gratuite
    BuyXGetY = 4        // Achetez X obtenez Y gratuit
}

public class CouponUsage : BaseEntity
{
    [Required]
    public int CouponId { get; set; }

    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [MaxLength(50)]
    public string? OrderId { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal DiscountAmount { get; set; }

    [Required]
    [MaxLength(3)]
    public required string Currency { get; set; } = "GNF";

    public DateTime UsedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual Coupon? Coupon { get; set; }
}
