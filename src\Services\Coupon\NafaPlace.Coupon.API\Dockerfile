# See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/Services/Coupon/NafaPlace.Coupon.API/NafaPlace.Coupon.API.csproj", "src/Services/Coupon/NafaPlace.Coupon.API/"]
COPY ["src/Services/Coupon/NafaPlace.Coupon.Application/NafaPlace.Coupon.Application.csproj", "src/Services/Coupon/NafaPlace.Coupon.Application/"]
COPY ["src/Services/Coupon/NafaPlace.Coupon.Domain/NafaPlace.Coupon.Domain.csproj", "src/Services/Coupon/NafaPlace.Coupon.Domain/"]
COPY ["src/BuildingBlocks/Common/NafaPlace.Common/NafaPlace.Common.csproj", "src/BuildingBlocks/Common/NafaPlace.Common/"]
COPY ["src/Services/Coupon/NafaPlace.Coupon.Infrastructure/NafaPlace.Coupon.Infrastructure.csproj", "src/Services/Coupon/NafaPlace.Coupon.Infrastructure/"]
RUN dotnet restore "./src/Services/Coupon/NafaPlace.Coupon.API/NafaPlace.Coupon.API.csproj"
COPY . .
WORKDIR "/src/src/Services/Coupon/NafaPlace.Coupon.API"
RUN dotnet build "./NafaPlace.Coupon.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./NafaPlace.Coupon.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "NafaPlace.Coupon.API.dll"]
