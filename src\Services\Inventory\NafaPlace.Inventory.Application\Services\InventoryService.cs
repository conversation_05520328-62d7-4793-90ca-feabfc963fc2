using Microsoft.Extensions.Logging;
using NafaPlace.Inventory.Application.DTOs;
using NafaPlace.Inventory.Domain.Models;
using NafaPlace.Inventory.Infrastructure.Repositories;

namespace NafaPlace.Inventory.Application.Services;

public class InventoryService : IInventoryService
{
    private readonly IInventoryRepository _repository;
    private readonly IProductStockRepository _productStockRepository;
    private readonly ILogger<InventoryService> _logger;

    public InventoryService(
        IInventoryRepository repository,
        IProductStockRepository productStockRepository,
        ILogger<InventoryService> logger)
    {
        _repository = repository;
        _productStockRepository = productStockRepository;
        _logger = logger;
    }

    public async Task<StockReservationDto> CreateReservationAsync(CreateReservationRequest request)
    {
        // Valider la disponibilité du stock
        var validation = await ValidateStockAvailabilityAsync(request.ProductId, request.Quantity);
        if (!validation.IsValid)
        {
            throw new InvalidOperationException(validation.ErrorMessage ?? "Stock insuffisant");
        }

        var reservation = new StockReservation
        {
            ProductId = request.ProductId,
            UserId = request.UserId,
            SessionId = request.SessionId,
            Quantity = request.Quantity,
            Status = ReservationStatus.Active,
            ReservedAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.AddMinutes(request.ExpirationMinutes),
            Reason = request.Reason ?? "Ajout au panier"
        };

        var createdReservation = await _repository.CreateReservationAsync(reservation);
        
        _logger.LogInformation("Stock reservation created: ProductId={ProductId}, Quantity={Quantity}, UserId={UserId}", 
            request.ProductId, request.Quantity, request.UserId);

        return MapReservationToDto(createdReservation);
    }

    public async Task<bool> ConfirmReservationAsync(int reservationId, string orderId)
    {
        var reservation = await _repository.GetReservationAsync(reservationId);
        if (reservation == null || !reservation.IsActive)
        {
            return false;
        }

        reservation.Status = ReservationStatus.Confirmed;
        reservation.OrderId = orderId;

        var success = await _repository.UpdateReservationAsync(reservation);
        
        if (success)
        {
            // Enregistrer le mouvement de stock
            await RecordMovementAsync(
                reservation.ProductId,
                MovementType.Sale,
                reservation.Quantity,
                $"Vente confirmée - Commande {orderId}",
                reservation.UserId,
                orderId
            );

            _logger.LogInformation("Stock reservation confirmed: ReservationId={ReservationId}, OrderId={OrderId}", 
                reservationId, orderId);
        }

        return success;
    }

    public async Task<bool> ReleaseReservationAsync(int reservationId, string reason)
    {
        var reservation = await _repository.GetReservationAsync(reservationId);
        if (reservation == null || reservation.Status != ReservationStatus.Active)
        {
            return false;
        }

        reservation.Status = ReservationStatus.Released;
        reservation.ReleasedAt = DateTime.UtcNow;
        reservation.Reason = reason;

        var success = await _repository.UpdateReservationAsync(reservation);
        
        if (success)
        {
            // Enregistrer le mouvement de libération
            await RecordMovementAsync(
                reservation.ProductId,
                MovementType.Release,
                reservation.Quantity,
                $"Libération de réservation - {reason}",
                reservation.UserId,
                reservationId.ToString()
            );

            _logger.LogInformation("Stock reservation released: ReservationId={ReservationId}, Reason={Reason}", 
                reservationId, reason);
        }

        return success;
    }

    public async Task<bool> ReleaseExpiredReservationsAsync()
    {
        var expiredReservations = await _repository.GetExpiredReservationsAsync();
        var count = 0;

        foreach (var reservation in expiredReservations)
        {
            reservation.Status = ReservationStatus.Expired;
            reservation.ReleasedAt = DateTime.UtcNow;
            
            if (await _repository.UpdateReservationAsync(reservation))
            {
                await RecordMovementAsync(
                    reservation.ProductId,
                    MovementType.Release,
                    reservation.Quantity,
                    "Libération automatique - Réservation expirée",
                    "System",
                    reservation.Id.ToString()
                );
                count++;
            }
        }

        if (count > 0)
        {
            _logger.LogInformation("Released {Count} expired reservations", count);
        }

        return count > 0;
    }

    public async Task<List<StockReservationDto>> GetUserReservationsAsync(string userId)
    {
        var reservations = await _repository.GetUserReservationsAsync(userId);
        return reservations.Select(MapReservationToDto).ToList();
    }

    public async Task<List<StockReservationDto>> GetProductReservationsAsync(int productId)
    {
        var reservations = await _repository.GetProductReservationsAsync(productId);
        return reservations.Select(MapReservationToDto).ToList();
    }

    public async Task<StockReservationDto?> GetReservationAsync(int reservationId)
    {
        var reservation = await _repository.GetReservationAsync(reservationId);
        return reservation != null ? MapReservationToDto(reservation) : null;
    }

    public async Task<StockValidationResult> ValidateStockAvailabilityAsync(int productId, int quantity)
    {
        var currentStock = await _productStockRepository.GetCurrentStockAsync(productId);
        var reservedStock = await GetReservedStockAsync(productId);
        var availableStock = Math.Max(0, currentStock - reservedStock);

        var result = new StockValidationResult
        {
            AvailableStock = availableStock,
            ReservedStock = reservedStock,
            RequestedQuantity = quantity,
            IsValid = availableStock >= quantity
        };

        if (!result.IsValid)
        {
            if (currentStock <= 0)
            {
                result.ErrorMessage = "Produit en rupture de stock";
            }
            else if (availableStock <= 0)
            {
                result.ErrorMessage = "Stock entièrement réservé";
            }
            else
            {
                result.ErrorMessage = $"Stock insuffisant. Disponible: {availableStock}, Demandé: {quantity}";
            }
        }

        return result;
    }

    public async Task<bool> IsStockAvailableAsync(int productId, int quantity)
    {
        var validation = await ValidateStockAvailabilityAsync(productId, quantity);
        return validation.IsValid;
    }

    public async Task<int> GetAvailableStockAsync(int productId)
    {
        var currentStock = await _productStockRepository.GetCurrentStockAsync(productId);
        var reservedStock = await GetReservedStockAsync(productId);
        return Math.Max(0, currentStock - reservedStock);
    }

    public async Task<int> GetReservedStockAsync(int productId)
    {
        return await _repository.GetReservedStockAsync(productId);
    }

    public async Task<bool> UpdateStockAsync(int productId, int newQuantity, string reason, string userId)
    {
        var currentStock = await _productStockRepository.GetCurrentStockAsync(productId);
        var success = await _productStockRepository.UpdateStockAsync(productId, newQuantity);

        if (success)
        {
            // Enregistrer le mouvement
            var movementType = newQuantity > currentStock ? MovementType.Restock : MovementType.Adjustment;
            var quantity = Math.Abs(newQuantity - currentStock);
            
            await RecordMovementAsync(productId, movementType, quantity, reason, userId);

            // Vérifier les alertes de stock
            await CheckStockAlertsAsync(productId, newQuantity);
        }

        return success;
    }

    public async Task<bool> AdjustStockAsync(StockAdjustmentRequest request, string userId)
    {
        return await UpdateStockAsync(request.ProductId, request.NewQuantity, request.Reason, userId);
    }

    public async Task<bool> BulkUpdateStockAsync(BulkStockUpdateRequest request, string userId)
    {
        var success = true;
        foreach (var item in request.Items)
        {
            var reason = !string.IsNullOrEmpty(item.ProductSpecificReason) 
                ? item.ProductSpecificReason 
                : request.Reason;
            
            if (!await UpdateStockAsync(item.ProductId, item.NewQuantity, reason, userId))
            {
                success = false;
                _logger.LogError("Failed to update stock for product {ProductId}", item.ProductId);
            }
        }
        return success;
    }

    public async Task<bool> ReserveStockForCartAsync(string userId, string sessionId, List<CartItemForReservation> items)
    {
        var success = true;
        foreach (var item in items)
        {
            try
            {
                var request = new CreateReservationRequest
                {
                    ProductId = item.ProductId,
                    UserId = userId,
                    SessionId = sessionId,
                    Quantity = item.Quantity,
                    ExpirationMinutes = 15,
                    Reason = $"Réservation panier - {item.ProductName}"
                };

                await CreateReservationAsync(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to reserve stock for product {ProductId}", item.ProductId);
                success = false;
            }
        }
        return success;
    }

    // Méthodes manquantes - Partie 1
    public async Task<List<StockAlertDto>> GetActiveAlertsAsync(int? sellerId = null)
    {
        var alerts = await _repository.GetActiveAlertsAsync(sellerId);
        return alerts.Select(MapAlertToDto).ToList();
    }

    public async Task<List<StockAlertDto>> GetSellerAlertsAsync(int sellerId)
    {
        var alerts = await _repository.GetSellerAlertsAsync(sellerId);
        return alerts.Select(MapAlertToDto).ToList();
    }

    public async Task<bool> AcknowledgeAlertAsync(int alertId, string userId)
    {
        var alert = await _repository.GetAlertAsync(alertId);
        if (alert == null) return false;

        alert.IsAcknowledged = true;
        alert.AcknowledgedBy = userId;
        alert.AcknowledgedAt = DateTime.UtcNow;

        return await _repository.UpdateAlertAsync(alert);
    }

    public async Task<bool> CreateLowStockAlertAsync(int productId, int currentStock, int threshold)
    {
        var product = await _productStockRepository.GetProductInfoAsync(productId);
        if (product == null) return false;

        var alert = new StockAlert
        {
            ProductId = productId,
            ProductName = product.Name,
            Type = AlertType.LowStock,
            Severity = currentStock <= threshold / 2 ? AlertSeverity.Critical : AlertSeverity.Warning,
            Message = $"Stock faible pour {product.Name}. Stock actuel: {currentStock}, Seuil: {threshold}",
            CurrentStock = currentStock,
            ThresholdValue = threshold,
            SellerId = product.SellerId,
            SellerName = product.SellerName
        };

        var createdAlert = await _repository.CreateAlertAsync(alert);
        return createdAlert != null;
    }

    public async Task<bool> CreateOutOfStockAlertAsync(int productId)
    {
        var product = await _productStockRepository.GetProductInfoAsync(productId);
        if (product == null) return false;

        var alert = new StockAlert
        {
            ProductId = productId,
            ProductName = product.Name,
            Type = AlertType.OutOfStock,
            Severity = AlertSeverity.Emergency,
            Message = $"Rupture de stock pour {product.Name}",
            CurrentStock = 0,
            SellerId = product.SellerId,
            SellerName = product.SellerName
        };

        var createdAlert = await _repository.CreateAlertAsync(alert);
        return createdAlert != null;
    }

    public async Task<int> ProcessPendingAlertsAsync()
    {
        var pendingAlerts = await _repository.GetPendingAlertsAsync();
        var processedCount = 0;

        foreach (var alert in pendingAlerts)
        {
            try
            {
                alert.IsActive = true;
                await _repository.UpdateAlertAsync(alert);
                processedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process alert {AlertId}", alert.Id);
            }
        }

        return processedCount;
    }

    public async Task<List<StockMovementDto>> GetProductMovementsAsync(int productId, int page = 1, int pageSize = 20)
    {
        var movements = await _repository.GetProductMovementsAsync(productId, page, pageSize);
        return movements.Select(MapMovementToDto).ToList();
    }

    public async Task<List<StockMovementDto>> GetSellerMovementsAsync(int sellerId, int page = 1, int pageSize = 20)
    {
        var movements = await _repository.GetSellerMovementsAsync(sellerId, page, pageSize);
        return movements.Select(MapMovementToDto).ToList();
    }

    public async Task<StockMovementDto> RecordMovementAsync(int productId, MovementType type, int quantity, string reason, string userId, string? reference = null)
    {
        var product = await _productStockRepository.GetProductInfoAsync(productId);
        var currentStock = await _productStockRepository.GetCurrentStockAsync(productId);

        var movement = new StockMovement
        {
            ProductId = productId,
            ProductName = product?.Name ?? "Produit inconnu",
            Type = type,
            Quantity = quantity,
            PreviousStock = currentStock,
            NewStock = type == MovementType.Sale ? currentStock - quantity : currentStock + quantity,
            Reason = reason,
            Reference = reference,
            UserId = userId,
            UserName = userId,
            SellerId = product?.SellerId ?? 0
        };

        var createdMovement = await _repository.CreateMovementAsync(movement);
        return MapMovementToDto(createdMovement);
    }

    // Méthodes privées d'aide
    private async Task CheckStockAlertsAsync(int productId, int newStock)
    {
        var config = await GetAlertConfigAsync(productId);
        if (config == null) return;

        if (newStock <= 0 && config.EnableOutOfStockAlerts)
        {
            await CreateOutOfStockAlertAsync(productId);
        }
        else if (newStock <= config.LowStockThreshold && config.EnableLowStockAlerts)
        {
            await CreateLowStockAlertAsync(productId, newStock, config.LowStockThreshold);
        }
    }

    private static StockReservationDto MapReservationToDto(StockReservation reservation)
    {
        return new StockReservationDto
        {
            Id = reservation.Id,
            ProductId = reservation.ProductId,
            UserId = reservation.UserId,
            SessionId = reservation.SessionId,
            Quantity = reservation.Quantity,
            Status = reservation.Status,
            ReservedAt = reservation.ReservedAt,
            ExpiresAt = reservation.ExpiresAt,
            OrderId = reservation.OrderId,
            Reason = reservation.Reason,
            ReleasedAt = reservation.ReleasedAt,
            IsExpired = reservation.IsExpired,
            IsActive = reservation.IsActive,
            TimeRemaining = reservation.TimeRemaining
        };
    }

    // Méthodes manquantes - Partie 2
    public async Task<InventoryDashboardDto> GetInventoryDashboardAsync(int? sellerId = null)
    {
        var dashboard = new InventoryDashboardDto();

        dashboard.TotalProducts = await _productStockRepository.GetTotalProductsCountAsync(sellerId);
        dashboard.LowStockProducts = await _productStockRepository.GetLowStockCountAsync(sellerId, 10);
        dashboard.OutOfStockProducts = await _productStockRepository.GetOutOfStockCountAsync(sellerId);
        dashboard.ActiveReservations = await _repository.GetActiveReservationsCountAsync(sellerId);
        dashboard.PendingAlerts = await _repository.GetPendingAlertsCountAsync(sellerId);
        dashboard.TotalInventoryValue = await _productStockRepository.GetTotalInventoryValueAsync(sellerId);

        dashboard.RecentAlerts = (await GetActiveAlertsAsync(sellerId)).Take(5).ToList();
        dashboard.RecentMovements = (await GetSellerMovementsAsync(sellerId ?? 0, 1, 10)).Take(10).ToList();
        dashboard.TopSellingProducts = await GetTopSellingProductsAsync(sellerId, 5);
        dashboard.LowStockProducts = await GetLowStockProductsAsync(sellerId, 10);

        return dashboard;
    }

    public async Task<List<TopProductDto>> GetTopSellingProductsAsync(int? sellerId = null, int count = 10)
    {
        return await _repository.GetTopSellingProductsAsync(sellerId, count);
    }

    public async Task<List<TopProductDto>> GetLowStockProductsAsync(int? sellerId = null, int threshold = 10)
    {
        return await _repository.GetLowStockProductsAsync(sellerId, threshold);
    }

    public async Task<bool> UpdateAlertConfigAsync(int productId, StockAlertConfigDto config)
    {
        return await Task.FromResult(true);
    }

    public async Task<StockAlertConfigDto?> GetAlertConfigAsync(int productId)
    {
        return await Task.FromResult(new StockAlertConfigDto
        {
            ProductId = productId,
            LowStockThreshold = 10,
            CriticalStockThreshold = 5,
            EnableLowStockAlerts = true,
            EnableOutOfStockAlerts = true,
            EnableEmailNotifications = true
        });
    }

    public async Task<int> CleanupExpiredReservationsAsync()
    {
        var expiredCount = await _repository.CleanupExpiredReservationsAsync();
        _logger.LogInformation("Cleaned up {Count} expired reservations", expiredCount);
        return expiredCount;
    }

    public async Task<int> CleanupOldMovementsAsync(int daysToKeep = 90)
    {
        var cleanedCount = await _repository.CleanupOldMovementsAsync(daysToKeep);
        _logger.LogInformation("Cleaned up {Count} old movements", cleanedCount);
        return cleanedCount;
    }

    public async Task<bool> RecalculateStockLevelsAsync()
    {
        return await _productStockRepository.RecalculateStockLevelsAsync();
    }

    private static StockAlertDto MapAlertToDto(StockAlert alert)
    {
        return new StockAlertDto
        {
            Id = alert.Id,
            ProductId = alert.ProductId,
            ProductName = alert.ProductName,
            Type = alert.Type,
            Severity = alert.Severity,
            Message = alert.Message,
            CurrentStock = alert.CurrentStock,
            ThresholdValue = alert.ThresholdValue,
            IsActive = alert.IsActive,
            IsAcknowledged = alert.IsAcknowledged,
            AcknowledgedBy = alert.AcknowledgedBy,
            AcknowledgedAt = alert.AcknowledgedAt,
            SellerId = alert.SellerId,
            SellerName = alert.SellerName,
            CreatedAt = alert.CreatedAt
        };
    }

    private static StockMovementDto MapMovementToDto(StockMovement movement)
    {
        return new StockMovementDto
        {
            Id = movement.Id,
            ProductId = movement.ProductId,
            ProductName = movement.ProductName,
            Type = movement.Type,
            Quantity = movement.Quantity,
            PreviousStock = movement.PreviousStock,
            NewStock = movement.NewStock,
            Reason = movement.Reason,
            Reference = movement.Reference,
            UserId = movement.UserId,
            UserName = movement.UserName,
            SellerId = movement.SellerId,
            Notes = movement.Notes,
            CreatedAt = movement.CreatedAt
        };
    }
}
