using Microsoft.JSInterop;
using NafaPlace.Web.Models.Cart;

namespace NafaPlace.Web.Services
{
    public interface IGuestCartMergeService
    {
        Task<bool> MergeGuestCartOnLoginAsync(string authenticatedUserId);
        Task ClearGuestCartAsync();
    }

    public class GuestCartMergeService : IGuestCartMergeService
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly ICartService _cartService;

        public GuestCartMergeService(IJSRuntime jsRuntime, ICartService cartService)
        {
            _jsRuntime = jsRuntime;
            _cartService = cartService;
        }

        public async Task<bool> MergeGuestCartOnLoginAsync(string authenticatedUserId)
        {
            try
            {
                // Récupérer l'ID invité du localStorage
                var guestId = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", "guestUserId");
                
                if (string.IsNullOrEmpty(guestId))
                {
                    return false; // Pas de panier invité à fusionner
                }

                // Récupérer le panier invité
                var guestCart = await _cartService.GetCartAsync(guestId);
                
                if (guestCart?.Items?.Any() != true)
                {
                    // Pas d'articles dans le panier invité
                    await ClearGuestCartAsync();
                    return false;
                }

                // Récupérer le panier de l'utilisateur authentifié
                var userCart = await _cartService.GetCartAsync(authenticatedUserId);

                // Fusionner les articles du panier invité dans le panier utilisateur
                foreach (var guestItem in guestCart.Items)
                {
                    var cartItem = new CartItemCreateDto
                    {
                        ProductId = guestItem.ProductId,
                        ProductName = guestItem.ProductName,
                        Price = guestItem.UnitPrice,
                        Quantity = guestItem.Quantity,
                        VariantId = guestItem.VariantId,
                        VariantName = guestItem.VariantName
                    };

                    await _cartService.AddItemToCartAsync(authenticatedUserId, cartItem);
                }

                // Nettoyer le panier invité
                await _cartService.ClearCartAsync(guestId);
                await ClearGuestCartAsync();

                return true; // Fusion réussie
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de la fusion des paniers: {ex.Message}");
                return false;
            }
        }

        public async Task ClearGuestCartAsync()
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", "guestUserId");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors du nettoyage du panier invité: {ex.Message}");
            }
        }
    }
}
