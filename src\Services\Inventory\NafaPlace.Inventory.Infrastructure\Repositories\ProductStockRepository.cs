using Microsoft.EntityFrameworkCore;
using NafaPlace.Inventory.Infrastructure.Data;

namespace NafaPlace.Inventory.Infrastructure.Repositories;

public class ProductStockRepository : IProductStockRepository
{
    private readonly InventoryDbContext _context;
    private readonly HttpClient _httpClient;

    public ProductStockRepository(InventoryDbContext context, HttpClient httpClient)
    {
        _context = context;
        _httpClient = httpClient;
    }

    public async Task<int> GetCurrentStockAsync(int productId)
    {
        try
        {
            // Appeler l'API Catalog pour obtenir le stock actuel
            var response = await _httpClient.GetAsync($"api/products/{productId}/stock");
            if (response.IsSuccessStatusCode)
            {
                var stockText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(stockText, out var stock))
                {
                    return stock;
                }
            }
        }
        catch (Exception)
        {
            // En cas d'erreur, retourner 0
        }

        return 0;
    }

    public async Task<bool> UpdateStockAsync(int productId, int newQuantity)
    {
        try
        {
            // Appeler l'API Catalog pour mettre à jour le stock
            var content = new StringContent(newQuantity.ToString(), System.Text.Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync($"api/products/{productId}/stock", content);
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public async Task<ProductInfo?> GetProductInfoAsync(int productId)
    {
        try
        {
            // Appeler l'API Catalog pour obtenir les informations du produit
            var response = await _httpClient.GetAsync($"api/products/{productId}");
            if (response.IsSuccessStatusCode)
            {
                var productJson = await response.Content.ReadAsStringAsync();
                // Ici on devrait désérialiser le JSON
                // Pour l'instant, on retourne des données simulées
                return new ProductInfo
                {
                    Id = productId,
                    Name = $"Produit {productId}",
                    Price = 100000,
                    SellerId = 1,
                    SellerName = "Vendeur Test",
                    CurrentStock = await GetCurrentStockAsync(productId)
                };
            }
        }
        catch (Exception)
        {
            // En cas d'erreur
        }

        return null;
    }

    public async Task<int> GetTotalProductsCountAsync(int? sellerId = null)
    {
        try
        {
            var url = sellerId.HasValue 
                ? $"api/products/count?sellerId={sellerId}" 
                : "api/products/count";
            
            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var countText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(countText, out var count))
                {
                    return count;
                }
            }
        }
        catch (Exception)
        {
            // En cas d'erreur
        }

        return 0;
    }

    public async Task<int> GetLowStockCountAsync(int? sellerId = null, int threshold = 10)
    {
        try
        {
            var url = sellerId.HasValue 
                ? $"api/products/low-stock-count?threshold={threshold}&sellerId={sellerId}" 
                : $"api/products/low-stock-count?threshold={threshold}";
            
            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var countText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(countText, out var count))
                {
                    return count;
                }
            }
        }
        catch (Exception)
        {
            // En cas d'erreur
        }

        return 0;
    }

    public async Task<int> GetOutOfStockCountAsync(int? sellerId = null)
    {
        try
        {
            var url = sellerId.HasValue 
                ? $"api/products/out-of-stock-count?sellerId={sellerId}" 
                : "api/products/out-of-stock-count";
            
            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var countText = await response.Content.ReadAsStringAsync();
                if (int.TryParse(countText, out var count))
                {
                    return count;
                }
            }
        }
        catch (Exception)
        {
            // En cas d'erreur
        }

        return 0;
    }

    public async Task<decimal> GetTotalInventoryValueAsync(int? sellerId = null)
    {
        try
        {
            var url = sellerId.HasValue 
                ? $"api/products/inventory-value?sellerId={sellerId}" 
                : "api/products/inventory-value";
            
            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var valueText = await response.Content.ReadAsStringAsync();
                if (decimal.TryParse(valueText, out var value))
                {
                    return value;
                }
            }
        }
        catch (Exception)
        {
            // En cas d'erreur
        }

        return 0;
    }

    public async Task<bool> RecalculateStockLevelsAsync()
    {
        try
        {
            var response = await _httpClient.PostAsync("api/products/recalculate-stock", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception)
        {
            return false;
        }
    }
}
