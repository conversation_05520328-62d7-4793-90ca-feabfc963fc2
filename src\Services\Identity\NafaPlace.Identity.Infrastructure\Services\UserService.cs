using Microsoft.EntityFrameworkCore;
using NafaPlace.Identity.Application.Common.Exceptions;
using NafaPlace.Identity.Application.Common.Interfaces;
using NafaPlace.Identity.Application.Common.Models;
using NafaPlace.Identity.Application.DTOs;
using NafaPlace.Identity.Domain.Models;
using NafaPlace.Identity.Infrastructure.Data;
using System.Security.Cryptography;
using System.Text;
using BCrypt.Net;

namespace NafaPlace.Identity.Infrastructure.Services;

public class UserService : IUserService
{
    private readonly IdentityDbContext _dbContext;

    public UserService(IdentityDbContext dbContext)
    {
        _dbContext = dbContext;
    }

    public async Task<PagedResult<UserDto>> GetUsersAsync(int pageNumber = 1, int pageSize = 20, string? searchTerm = null)
    {
        var query = _dbContext.Users.AsQueryable();

        // Appliquer le filtre de recherche si fourni
        if (!string.IsNullOrEmpty(searchTerm))
        {
            query = query.Where(u => u.Email.Contains(searchTerm) ||
                                   u.Username.Contains(searchTerm) ||
                                   u.FirstName.Contains(searchTerm) ||
                                   u.LastName.Contains(searchTerm));
        }

        // Calculer le nombre total d'éléments
        var totalCount = await query.CountAsync();

        // Appliquer la pagination
        var users = await query
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();

        // Convertir en DTOs
        var userDtos = new List<UserDto>();
        foreach (var user in users)
        {
            // Récupérer les rôles de l'utilisateur
            var userRoles = await _dbContext.UserRoles
                .Where(ur => ur.UserId == user.Id)
                .Join(_dbContext.Roles,
                      ur => ur.RoleId,
                      r => r.Id,
                      (ur, r) => r.Name)
                .ToListAsync();

            userDtos.Add(new UserDto
            {
                Id = user.Id,
                Email = user.Email,
                Username = user.Username,
                FirstName = user.FirstName,
                LastName = user.LastName,
                IsActive = user.IsActive,
                CreatedAt = user.CreatedAt,
                UpdatedAt = user.UpdatedAt,
                Roles = userRoles
            });
        }

        return new PagedResult<UserDto>
        {
            Items = userDtos,
            TotalCount = totalCount,
            PageNumber = pageNumber,
            PageSize = pageSize
        };
    }

    public async Task<UserDto> GetUserProfileAsync(int userId)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);
        
        if (user == null)
        {
            throw new AuthenticationException("Utilisateur non trouvé");
        }

        // Récupérer les rôles de l'utilisateur
        var userRoles = await _dbContext.UserRoles
            .Where(ur => ur.UserId == userId)
            .Join(_dbContext.Roles,
                  ur => ur.RoleId,
                  r => r.Id,
                  (ur, r) => r.Name)
            .ToListAsync();

        return new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            Username = user.Username,
            FirstName = user.FirstName,
            LastName = user.LastName,
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            Roles = userRoles
        };
    }

    public async Task<UserDto> UpdateUserProfileAsync(int userId, UpdateUserProfileRequest request)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);
        
        if (user == null)
        {
            throw new AuthenticationException("Utilisateur non trouvé");
        }

        // Mettre à jour les informations du profil
        user.FirstName = request.FirstName;
        user.LastName = request.LastName;
        user.PhoneNumber = request.PhoneNumber ?? user.PhoneNumber;
        user.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();

        // Récupérer les rôles de l'utilisateur
        var userRoles = await _dbContext.UserRoles
            .Where(ur => ur.UserId == userId)
            .Join(_dbContext.Roles,
                  ur => ur.RoleId,
                  r => r.Id,
                  (ur, r) => r.Name)
            .ToListAsync();

        return new UserDto
        {
            Id = user.Id,
            Email = user.Email,
            Username = user.Username,
            FirstName = user.FirstName,
            LastName = user.LastName,
            IsActive = user.IsActive,
            CreatedAt = user.CreatedAt,
            UpdatedAt = user.UpdatedAt,
            Roles = userRoles
        };
    }

    public async Task ChangePasswordAsync(int userId, ChangePasswordRequest request)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);
        
        if (user == null)
        {
            throw new AuthenticationException("Utilisateur non trouvé");
        }

        // Vérifier le mot de passe actuel
        if (!VerifyPasswordHash(request.CurrentPassword, user.PasswordHash))
        {
            throw new AuthenticationException("Le mot de passe actuel est incorrect");
        }

        // Mettre à jour le mot de passe
        user.PasswordHash = HashPassword(request.NewPassword);
        user.UpdatedAt = DateTime.UtcNow;

        await _dbContext.SaveChangesAsync();
    }

    private string HashPassword(string password)
    {
        // Utilisation de BCrypt pour le hachage du mot de passe
        return BCrypt.Net.BCrypt.HashPassword(password);
    }

    private bool VerifyPasswordHash(string password, string storedHash)
    {
        try
        {
            // Journalisation pour le débogage
            Console.WriteLine($"Vérification du mot de passe");
            Console.WriteLine($"Mot de passe stocké: {storedHash}");
            
            // Vérification avec BCrypt
            var isValid = BCrypt.Net.BCrypt.Verify(password, storedHash);
            Console.WriteLine($"Résultat de la vérification: {isValid}");
            
            return isValid;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la vérification du mot de passe: {ex.Message}");
            return false;
        }
    }
}
