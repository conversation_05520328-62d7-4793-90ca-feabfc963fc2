using System.ComponentModel.DataAnnotations;
using NafaPlace.Common.Models;

namespace NafaPlace.Notifications.Domain.Models;

public class NotificationTemplate : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public required string Name { get; set; }

    [Required]
    [MaxLength(50)]
    public required string Code { get; set; }

    [Required]
    public NotificationType Type { get; set; }

    [Required]
    public NotificationChannel Channel { get; set; }

    [Required]
    [MaxLength(200)]
    public required string Subject { get; set; }

    [Required]
    public required string Body { get; set; }

    [MaxLength(500)]
    public string? Description { get; set; }

    public bool IsActive { get; set; } = true;

    [Required]
    [MaxLength(10)]
    public required string Language { get; set; } = "fr";

    // Template variables (JSON format)
    public string? Variables { get; set; }

    [MaxLength(50)]
    public string? CreatedBy { get; set; }

    [MaxLength(50)]
    public string? UpdatedBy { get; set; }

    // Navigation properties
    public virtual ICollection<NotificationLog> Notifications { get; set; } = new List<NotificationLog>();
}

public class NotificationLog : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [Required]
    [MaxLength(100)]
    public required string UserName { get; set; }

    [Required]
    [MaxLength(100)]
    public required string Recipient { get; set; }

    [Required]
    public NotificationChannel Channel { get; set; }

    [Required]
    public NotificationType Type { get; set; }

    [Required]
    [MaxLength(200)]
    public required string Subject { get; set; }

    [Required]
    public required string Content { get; set; }

    [Required]
    public NotificationStatus Status { get; set; } = NotificationStatus.Pending;

    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;

    public DateTime? ScheduledAt { get; set; }

    public DateTime? SentAt { get; set; }

    public DateTime? DeliveredAt { get; set; }

    public DateTime? ReadAt { get; set; }

    [MaxLength(500)]
    public string? ErrorMessage { get; set; }

    public int RetryCount { get; set; } = 0;

    public int MaxRetries { get; set; } = 3;

    [MaxLength(50)]
    public string? Reference { get; set; } // Order ID, etc.

    [MaxLength(50)]
    public string? TemplateCode { get; set; }

    // Metadata (JSON format)
    public string? Metadata { get; set; }

    // Navigation properties
    public virtual NotificationTemplate? Template { get; set; }
}

public enum NotificationType
{
    Welcome = 1,                    // Bienvenue
    OrderConfirmation = 2,          // Confirmation de commande
    OrderStatusUpdate = 3,          // Mise à jour statut commande
    PaymentConfirmation = 4,        // Confirmation de paiement
    PaymentFailed = 5,              // Échec de paiement
    ShippingNotification = 6,       // Notification d'expédition
    DeliveryConfirmation = 7,       // Confirmation de livraison
    StockAlert = 8,                 // Alerte de stock
    LowStockWarning = 9,            // Avertissement stock faible
    ProductAvailable = 10,          // Produit disponible
    PriceAlert = 11,                // Alerte de prix
    PromotionalOffer = 12,          // Offre promotionnelle
    NewsletterUpdate = 13,          // Newsletter
    AccountVerification = 14,       // Vérification de compte
    PasswordReset = 15,             // Réinitialisation mot de passe
    SecurityAlert = 16,             // Alerte de sécurité
    ReviewRequest = 17,             // Demande d'avis
    WishlistReminder = 18,          // Rappel liste de souhaits
    AbandonedCart = 19,             // Panier abandonné
    ReturnRequest = 20,             // Demande de retour
    RefundProcessed = 21,           // Remboursement traité
    SystemMaintenance = 22,         // Maintenance système
    Custom = 99                     // Personnalisé
}

public enum NotificationChannel
{
    Email = 1,      // Email
    SMS = 2,        // SMS
    Push = 3,       // Notification push
    InApp = 4,      // Notification in-app
    WhatsApp = 5,   // WhatsApp
    Telegram = 6    // Telegram
}

public enum NotificationStatus
{
    Pending = 1,    // En attente
    Sent = 2,       // Envoyé
    Delivered = 3,  // Livré
    Read = 4,       // Lu
    Failed = 5,     // Échec
    Cancelled = 6,  // Annulé
    Expired = 7     // Expiré
}

public enum NotificationPriority
{
    Low = 1,        // Basse
    Normal = 2,     // Normale
    High = 3,       // Haute
    Critical = 4    // Critique
}

public class NotificationPreference : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public required string UserId { get; set; }

    [Required]
    public NotificationType Type { get; set; }

    [Required]
    public NotificationChannel Channel { get; set; }

    public bool IsEnabled { get; set; } = true;

    public TimeSpan? QuietHoursStart { get; set; }

    public TimeSpan? QuietHoursEnd { get; set; }

    [MaxLength(10)]
    public string? TimeZone { get; set; }

    [MaxLength(10)]
    public string? Language { get; set; } = "fr";

    // Frequency settings
    public NotificationFrequency Frequency { get; set; } = NotificationFrequency.Immediate;

    public DateTime? LastSent { get; set; }
}

public enum NotificationFrequency
{
    Immediate = 1,  // Immédiat
    Hourly = 2,     // Toutes les heures
    Daily = 3,      // Quotidien
    Weekly = 4,     // Hebdomadaire
    Monthly = 5,    // Mensuel
    Never = 6       // Jamais
}

public class NotificationQueue : BaseEntity
{
    [Required]
    public int NotificationId { get; set; }

    [Required]
    public NotificationPriority Priority { get; set; } = NotificationPriority.Normal;

    [Required]
    public DateTime ScheduledAt { get; set; } = DateTime.UtcNow;

    public DateTime? ProcessedAt { get; set; }

    public int RetryCount { get; set; } = 0;

    [MaxLength(500)]
    public string? ErrorMessage { get; set; }

    public bool IsProcessing { get; set; } = false;

    // Navigation properties
    public virtual NotificationLog? Notification { get; set; }
}
