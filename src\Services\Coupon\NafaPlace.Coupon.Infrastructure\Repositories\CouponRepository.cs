using Microsoft.EntityFrameworkCore;
using NafaPlace.Coupon.Application.DTOs;
using NafaPlace.Coupon.Domain.Models;
using NafaPlace.Coupon.Infrastructure.Data;

namespace NafaPlace.Coupon.Infrastructure.Repositories;

public class CouponRepository : ICouponRepository
{
    private readonly CouponDbContext _context;

    public CouponRepository(CouponDbContext context)
    {
        _context = context;
    }

    public async Task<Domain.Models.Coupon> CreateAsync(Domain.Models.Coupon coupon)
    {
        _context.Coupons.Add(coupon);
        await _context.SaveChangesAsync();
        return coupon;
    }

    public async Task<Domain.Models.Coupon> UpdateAsync(Domain.Models.Coupon coupon)
    {
        _context.Coupons.Update(coupon);
        await _context.SaveChangesAsync();
        return coupon;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var coupon = await _context.Coupons.FindAsync(id);
        if (coupon == null) return false;

        _context.Coupons.Remove(coupon);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<Domain.Models.Coupon?> GetByIdAsync(int id)
    {
        return await _context.Coupons
            .Include(c => c.Usages)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<Domain.Models.Coupon?> GetByCodeAsync(string code)
    {
        return await _context.Coupons
            .Include(c => c.Usages)
            .FirstOrDefaultAsync(c => c.Code == code.ToUpper());
    }

    public async Task<List<Domain.Models.Coupon>> GetCouponsAsync(int page = 1, int pageSize = 20, bool? isActive = null)
    {
        var query = _context.Coupons.AsQueryable();

        if (isActive.HasValue)
        {
            query = query.Where(c => c.IsActive == isActive.Value);
        }

        return await query
            .OrderByDescending(c => c.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    public async Task<List<Domain.Models.Coupon>> GetActiveCouponsAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.Coupons
            .Where(c => c.IsActive && c.StartDate <= now && c.EndDate >= now)
            .Where(c => !c.UsageLimit.HasValue || c.UsageCount < c.UsageLimit.Value)
            .OrderBy(c => c.EndDate)
            .ToListAsync();
    }

    public async Task<List<Domain.Models.Coupon>> GetExpiredCouponsAsync()
    {
        var now = DateTime.UtcNow;
        return await _context.Coupons
            .Where(c => c.EndDate < now)
            .OrderByDescending(c => c.EndDate)
            .ToListAsync();
    }

    public async Task<bool> RecordUsageAsync(CouponUsage usage)
    {
        _context.CouponUsages.Add(usage);
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<bool> IncrementUsageCountAsync(int couponId)
    {
        var coupon = await _context.Coupons.FindAsync(couponId);
        if (coupon == null) return false;

        coupon.UsageCount++;
        await _context.SaveChangesAsync();
        return true;
    }

    public async Task<int> GetUserUsageCountAsync(int couponId, string userId)
    {
        return await _context.CouponUsages
            .CountAsync(u => u.CouponId == couponId && u.UserId == userId);
    }

    public async Task<CouponStatsResult> GetCouponStatsAsync(int couponId)
    {
        var usages = await _context.CouponUsages
            .Where(u => u.CouponId == couponId)
            .ToListAsync();

        return new CouponStatsResult
        {
            TotalUsages = usages.Count,
            TotalDiscountGiven = usages.Sum(u => u.DiscountAmount),
            UniqueUsers = usages.Select(u => u.UserId).Distinct().Count(),
            LastUsed = usages.Any() ? usages.Max(u => u.UsedAt) : null
        };
    }

    public async Task<List<CouponUsageStatsDto>> GetUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.CouponUsages.AsQueryable();

        if (startDate.HasValue)
        {
            query = query.Where(u => u.UsedAt >= startDate.Value);
        }

        if (endDate.HasValue)
        {
            query = query.Where(u => u.UsedAt <= endDate.Value);
        }

        var stats = await query
            .GroupBy(u => u.UsedAt.Date)
            .Select(g => new CouponUsageStatsDto
            {
                Date = g.Key,
                TotalCouponsUsed = g.Count(),
                TotalDiscountGiven = g.Sum(u => u.DiscountAmount),
                Currency = "GNF",
                UniqueCoupons = g.Select(u => u.CouponId).Distinct().Count(),
                UniqueUsers = g.Select(u => u.UserId).Distinct().Count()
            })
            .OrderBy(s => s.Date)
            .ToListAsync();

        return stats;
    }

    public async Task<int> CleanupExpiredCouponsAsync()
    {
        var expiredCoupons = await GetExpiredCouponsAsync();
        var inactiveCoupons = expiredCoupons.Where(c => c.IsActive).ToList();

        foreach (var coupon in inactiveCoupons)
        {
            coupon.IsActive = false;
        }

        if (inactiveCoupons.Any())
        {
            await _context.SaveChangesAsync();
        }

        return inactiveCoupons.Count;
    }
}
