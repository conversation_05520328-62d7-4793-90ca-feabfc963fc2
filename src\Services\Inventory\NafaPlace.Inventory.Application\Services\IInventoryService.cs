using NafaPlace.Inventory.Application.DTOs;

namespace NafaPlace.Inventory.Application.Services;

public interface IInventoryService
{
    // Stock Reservations
    Task<StockReservationDto> CreateReservationAsync(CreateReservationRequest request);
    Task<bool> ConfirmReservationAsync(int reservationId, string orderId);
    Task<bool> ReleaseReservationAsync(int reservationId, string reason);
    Task<bool> ReleaseExpiredReservationsAsync();
    Task<List<StockReservationDto>> GetUserReservationsAsync(string userId);
    Task<List<StockReservationDto>> GetProductReservationsAsync(int productId);
    Task<StockReservationDto?> GetReservationAsync(int reservationId);

    // Stock Validation
    Task<StockValidationResult> ValidateStockAvailabilityAsync(int productId, int quantity);
    Task<bool> IsStockAvailableAsync(int productId, int quantity);
    Task<int> GetAvailableStockAsync(int productId);
    Task<int> GetReservedStockAsync(int productId);

    // Stock Management
    Task<bool> UpdateStockAsync(int productId, int newQuantity, string reason, string userId);
    Task<bool> AdjustStockAsync(StockAdjustmentRequest request, string userId);
    Task<bool> BulkUpdateStockAsync(BulkStockUpdateRequest request, string userId);
    Task<bool> ReserveStockForCartAsync(string userId, string sessionId, List<CartItemForReservation> items);

    // Stock Alerts
    Task<List<StockAlertDto>> GetActiveAlertsAsync(int? sellerId = null);
    Task<List<StockAlertDto>> GetSellerAlertsAsync(int sellerId);
    Task<bool> AcknowledgeAlertAsync(int alertId, string userId);
    Task<bool> CreateLowStockAlertAsync(int productId, int currentStock, int threshold);
    Task<bool> CreateOutOfStockAlertAsync(int productId);
    Task<int> ProcessPendingAlertsAsync();

    // Stock Movements
    Task<List<StockMovementDto>> GetProductMovementsAsync(int productId, int page = 1, int pageSize = 20);
    Task<List<StockMovementDto>> GetSellerMovementsAsync(int sellerId, int page = 1, int pageSize = 20);
    Task<StockMovementDto> RecordMovementAsync(int productId, MovementType type, int quantity, string reason, string userId, string? reference = null);

    // Dashboard and Analytics
    Task<InventoryDashboardDto> GetInventoryDashboardAsync(int? sellerId = null);
    Task<List<TopProductDto>> GetTopSellingProductsAsync(int? sellerId = null, int count = 10);
    Task<List<TopProductDto>> GetLowStockProductsAsync(int? sellerId = null, int threshold = 10);

    // Configuration
    Task<bool> UpdateAlertConfigAsync(int productId, StockAlertConfigDto config);
    Task<StockAlertConfigDto?> GetAlertConfigAsync(int productId);

    // Maintenance
    Task<int> CleanupExpiredReservationsAsync();
    Task<int> CleanupOldMovementsAsync(int daysToKeep = 90);
    Task<bool> RecalculateStockLevelsAsync();
}

public class CartItemForReservation
{
    public int ProductId { get; set; }
    public int Quantity { get; set; }
    public string ProductName { get; set; } = string.Empty;
}
