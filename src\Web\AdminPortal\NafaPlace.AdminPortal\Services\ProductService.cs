﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Linq;
using System.Threading.Tasks;
using NafaPlace.AdminPortal.Models.Products;

namespace NafaPlace.AdminPortal.Services
{
    public class ProductService
    {
        private readonly HttpClient _httpClient;
        private int _lastCreatedProductId = 0; // Pour stocker l'ID du dernier produit crÃ©Ã©

        public ProductService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public async Task<List<ProductDto>> GetProductsAsync()
        {
            try
            {
                // RÃ©cupÃ©rer tous les produits avec une pagination Ã©levÃ©e
                var response = await _httpClient.GetAsync("/api/v1/products?page=1&pageSize=100");

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();

                    var products = new List<ProductDto>();
                    using (JsonDocument doc = JsonDocument.Parse(content))
                    {
                        // L'API retourne un objet paginÃ© avec une propriÃ©tÃ© "items"
                        if (doc.RootElement.TryGetProperty("items", out var itemsElement))
                        {
                            foreach (JsonElement element in itemsElement.EnumerateArray())
                            {
                                if (element.TryGetProperty("id", out var idElement))
                                {
                                    // RÃ©cupÃ©rer les dÃ©tails complets du produit, y compris les images
                                    var productId = idElement.GetInt32();
                                    var productDetail = await GetProductAsync(productId);
                                    if (productDetail != null)
                                    {
                                        products.Add(productDetail);
                                    }
                                }
                            }
                        }
                    }

                    return products;
                }
                else
                {
                    return new List<ProductDto>();
                }
            }
            catch (Exception)
            {
                return new List<ProductDto>();
            }
        }

        public async Task<ProductDto> GetProductAsync(int id)
        {
            try
            {
                var response = await _httpClient.GetAsync($"/api/v1/products/{id}");
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    
                    var apiProduct = JsonSerializer.Deserialize<JsonObject>(jsonString, options);
                    
                    if (apiProduct != null)
                    {
                        var product = new ProductDto
                        {
                            Id = apiProduct.TryGetPropertyValue("id", out var idNode) && idNode != null ? (int)(idNode) : 0,
                            Name = apiProduct.TryGetPropertyValue("name", out var nameNode) && nameNode != null ? nameNode.GetValue<string>() ?? string.Empty : string.Empty,
                            Description = apiProduct.TryGetPropertyValue("description", out var descNode) && descNode != null ? descNode.GetValue<string>() ?? string.Empty : string.Empty,
                            Price = apiProduct.TryGetPropertyValue("price", out var priceNode) && priceNode != null ? priceNode.GetValue<decimal>() : 0,
                            Currency = apiProduct.TryGetPropertyValue("currency", out var currNode) && currNode != null ? currNode.GetValue<string>() ?? "XOF" : "XOF",
                            Brand = apiProduct.TryGetPropertyValue("brand", out var brandNode) && brandNode != null ? brandNode.GetValue<string>() ?? string.Empty : string.Empty,
                            Model = apiProduct.TryGetPropertyValue("model", out var modelNode) && modelNode != null ? modelNode.GetValue<string>() ?? string.Empty : string.Empty,
                            Stock = apiProduct.TryGetPropertyValue("stockQuantity", out var stockNode) && stockNode != null ? stockNode.GetValue<int>() : 0,
                            CategoryId = apiProduct.TryGetPropertyValue("categoryId", out var catIdNode) && catIdNode != null ? catIdNode.GetValue<int>() : 0,
                            SellerId = apiProduct.TryGetPropertyValue("sellerId", out var sellIdNode) && sellIdNode != null ? sellIdNode.GetValue<int>() : 0,
                            IsActive = apiProduct.TryGetPropertyValue("isActive", out var activeNode) && activeNode != null ? activeNode.GetValue<bool>() : false,
                            IsFeatured = apiProduct.TryGetPropertyValue("isFeatured", out var featNode) && featNode != null ? featNode.GetValue<bool>() : false,
                            CreatedAt = apiProduct.TryGetPropertyValue("createdAt", out var createdAtNode) && createdAtNode != null ? createdAtNode.GetValue<DateTime>() : DateTime.Now,
                            UpdatedAt = apiProduct.TryGetPropertyValue("updatedAt", out var updatedAtNode) && updatedAtNode != null ? updatedAtNode.GetValue<DateTime>() : (DateTime?)null,
                            CategoryName = await GetCategoryNameAsync(apiProduct.TryGetPropertyValue("categoryId", out var catIdNode3) && catIdNode3 != null ? catIdNode3.GetValue<int>() : 0),
                            SellerName = await GetSellerNameAsync(apiProduct.TryGetPropertyValue("sellerId", out var sellIdNode3) && sellIdNode3 != null ? sellIdNode3.GetValue<int>() : 0),
                            ApprovalStatus = apiProduct.TryGetPropertyValue("isActive", out var activeNode2) && activeNode2 != null ? activeNode2.GetValue<bool>() ? "ApprouvÃ©" : "En attente" : "En attente"
                        };

                        // Traitement des images
                        if (apiProduct.TryGetPropertyValue("images", out var imagesNode) && imagesNode is JsonArray apiImages)
                        {
                            product.Images = new List<ProductImageDto>();
                            foreach (var imgItem in apiImages)
                            {
                                if (imgItem is JsonObject apiImage)
                                {
                                    var image = new ProductImageDto
                                    {
                                        Id = apiImage.TryGetPropertyValue("id", out var imgIdNode) && imgIdNode != null ? (int)(imgIdNode) : 0,
                                        ImageUrl = apiImage.TryGetPropertyValue("url", out var urlNode) && urlNode != null ? urlNode.GetValue<string>() ?? string.Empty : string.Empty,
                                        ThumbnailUrl = apiImage.TryGetPropertyValue("thumbnailUrl", out var thumbUrlNode) && thumbUrlNode != null ? thumbUrlNode.GetValue<string>() ?? string.Empty : string.Empty,
                                        IsMain = apiImage.TryGetPropertyValue("isMain", out var mainNode) && mainNode != null ? mainNode.GetValue<bool>() : false
                                    };
                                    
                                    product.Images.Add(image);
                                }
                            }
                        }

                        return product;
                    }
                }
                
                return new ProductDto();
            }
            catch (JsonException)
            {
                return new ProductDto();
            }
            catch (Exception)
            {
                return new ProductDto();
            }
        }

        public async Task<bool> CreateProductAsync(ProductDto product)
        {
            try
            {
                // CrÃ©er un objet JSON qui correspond Ã  la structure attendue par l'API
                var createRequest = new
                {
                    Name = product.Name,
                    Description = product.Description,
                    Price = product.Price,
                    Currency = product.Currency ?? "GNF",
                    StockQuantity = product.Stock,
                    CategoryId = product.CategoryId,
                    SellerId = product.SellerId,
                    Brand = product.Brand ?? string.Empty,
                    Model = product.Model ?? string.Empty,
                    Weight = 0.0, // Valeur par dÃ©faut
                    Dimensions = product.Dimensions ?? "0x0x0",
                    IsActive = product.IsActive,
                    IsFeatured = product.IsFeatured,
                    Images = new List<object>(),
                    Variants = new List<object>(),
                    Attributes = new List<object>()
                };

                var jsonString = JsonSerializer.Serialize(createRequest);
                var content = new StringContent(jsonString, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync("/api/v1/products", content);

                if (response.IsSuccessStatusCode)
                {
                    var responseJson = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
                    var apiProduct = JsonSerializer.Deserialize<JsonObject>(responseJson, options);
                    if (apiProduct != null)
                    {
                        _lastCreatedProductId = apiProduct.TryGetPropertyValue("id", out var idNode) && idNode != null ? (int)(idNode) : 0;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Erreur lors de la crÃ©ation du produit: {response.StatusCode}, DÃ©tails: {errorContent}");
                }

                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> UpdateProductAsync(ProductDto product)
        {
            try
            {
                // CrÃ©er un objet JSON qui correspond Ã  la structure attendue par l'API
                var productData = new
                {
                    name = product.Name,
                    description = product.Description,
                    price = product.Price,
                    currency = product.Currency,
                    stockQuantity = product.Stock,
                    categoryId = product.CategoryId,
                    sellerId = product.SellerId,
                    brand = product.Brand,
                    model = product.Model,
                    dimensions = product.Dimensions,
                    isActive = product.IsActive,
                    isFeatured = product.IsFeatured,
                    images = new object[0],
                    variants = product.Variants.Select(v => new
                    {
                        v.Id,
                        v.Name,
                        v.Price,
                        v.Stock,
                        v.Color,
                        v.Size
                    }).ToArray()
                };
                
                var options = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                var jsonContent = JsonSerializer.Serialize(productData, options);
                
                var content = new StringContent(
                    jsonContent,
                    Encoding.UTF8,
                    "application/json");
                
                var response = await _httpClient.PutAsync($"/api/v1/products/{product.Id}", content);
                
                if (!response.IsSuccessStatusCode)
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                }
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"/api/v1/products/{id}");
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<List<CategoryDto>> GetCategoriesAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/categories");
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    
                    var apiCategories = JsonSerializer.Deserialize<JsonArray>(jsonString, options);
                    var categories = new List<CategoryDto>();
                    
                    if (apiCategories != null)
                    {
                        foreach (var item in apiCategories)
                        {
                            if (item == null) continue;
                            
                            var apiCategory = item.AsObject();
                            if (apiCategory != null)
                            {
                                var category = new CategoryDto
                                {
                                    Id = apiCategory.TryGetPropertyValue("id", out var idNode) && idNode != null ? (int)(idNode) : 0,
                                    Name = apiCategory.TryGetPropertyValue("name", out var nameNode) && nameNode != null ? nameNode.GetValue<string>() ?? string.Empty : string.Empty,
                                    Description = apiCategory.TryGetPropertyValue("description", out var descNode) && descNode != null ? descNode.GetValue<string>() ?? string.Empty : string.Empty,
                                    ImageUrl = apiCategory.TryGetPropertyValue("imageUrl", out var imgNode) && imgNode != null ? imgNode.GetValue<string>() ?? string.Empty : string.Empty
                                };
                                
                                categories.Add(category);
                            }
                        }
                    }
                    
                    return categories;
                }
                
                return new List<CategoryDto>();
            }
            catch (Exception)
            {
                return new List<CategoryDto>();
            }
        }
        
        public async Task<List<SellerDto>> GetSellersAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("/api/v1/sellers");
                
                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    
                    var apiSellers = JsonSerializer.Deserialize<JsonArray>(jsonString, options);
                    var sellers = new List<SellerDto>();
                    
                    if (apiSellers != null)
                    {
                        foreach (var item in apiSellers)
                        {
                            if (item == null) continue;
                            
                            var apiSeller = item.AsObject();
                            if (apiSeller != null)
                            {
                                var seller = new SellerDto
                                {
                                    Id = apiSeller.TryGetPropertyValue("id", out var idNode) && idNode != null ? (int)(idNode) : 0,
                                    Name = apiSeller.TryGetPropertyValue("name", out var nameNode) && nameNode != null ? nameNode.GetValue<string>() ?? string.Empty : string.Empty,
                                    Email = apiSeller.TryGetPropertyValue("email", out var emailNode) && emailNode != null ? emailNode.GetValue<string>() ?? string.Empty : string.Empty,
                                    PhoneNumber = apiSeller.TryGetPropertyValue("phoneNumber", out var phoneNode) && phoneNode != null ? phoneNode.GetValue<string>() ?? string.Empty : string.Empty,
                                    Address = apiSeller.TryGetPropertyValue("address", out var addrNode) && addrNode != null ? addrNode.GetValue<string>() ?? string.Empty : string.Empty
                                };
                                
                                sellers.Add(seller);
                            }
                        }
                    }
                    
                    return sellers;
                }
                
                return new List<SellerDto>();
            }
            catch (Exception)
            {
                return new List<SellerDto>();
            }
        }
        
        public async Task<bool> ApproveProductAsync(int id)
        {
            try
            {
                var response = await _httpClient.PutAsync($"/api/v1/products/{id}/approve", null);
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }
        
        public async Task<bool> RejectProductAsync(int id, string reason)
        {
            try
            {
                var content = new StringContent(
                    JsonSerializer.Serialize(new { Reason = reason }),
                    Encoding.UTF8,
                    "application/json");
                
                var response = await _httpClient.PutAsync($"/api/v1/products/{id}/reject", content);
                
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public int GetLastCreatedProductId()
        {
            return _lastCreatedProductId;
        }

        private async Task<string> GetCategoryNameAsync(int categoryId)
        {
            try
            {
                var categories = await GetCategoriesAsync();
                var category = categories.FirstOrDefault(c => c.Id == categoryId);
                return category?.Name ?? "CatÃ©gorie inconnue";
            }
            catch
            {
                return "CatÃ©gorie inconnue";
            }
        }
        
        private async Task<string> GetSellerNameAsync(int sellerId)
        {
            try
            {
                var sellers = await GetSellersAsync();
                var seller = sellers.FirstOrDefault(s => s.Id == sellerId);
                return seller?.Name ?? "Vendeur inconnu";
            }
            catch
            {
                return "Vendeur inconnu";
            }
        }

        public string GetImageUrl(ProductImageDto image, bool thumbnail = false)
        {
            if (image == null)
            {
                return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
            }

            var url = thumbnail ? image.ThumbnailUrl : image.ImageUrl;

            if (string.IsNullOrEmpty(url))
            {
                // Fallback to the other url if one is empty
                url = thumbnail ? image.ImageUrl : image.ThumbnailUrl;
            }

            if (string.IsNullOrEmpty(url))
            {
                return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
            }

            if (url.StartsWith("http://") || url.StartsWith("https://") || url.StartsWith("data:"))
            {
                return url;
            }

            // Retourner l'URL relative pour les images
            return url.StartsWith("/") ? url : $"/{url}";
        }
    }
}

