using System;
using System.Threading.Tasks;
using Microsoft.JSInterop;

namespace NafaPlace.AdminPortal.Services
{
    public class NotificationService
    {
        private readonly IJSRuntime _jsRuntime;

        public NotificationService(IJSRuntime jsRuntime)
        {
            _jsRuntime = jsRuntime;
        }

        public async Task ShowSuccessAsync(string message)
        {
            await ShowToastAsync("success", "Succès", message);
        }

        public async Task ShowErrorAsync(string message)
        {
            await ShowToastAsync("error", "Erreur", message);
        }

        public async Task ShowWarningAsync(string message)
        {
            await ShowToastAsync("warning", "Attention", message);
        }

        public async Task ShowInfoAsync(string message)
        {
            await ShowToastAsync("info", "Information", message);
        }

        private async Task ShowToastAsync(string type, string title, string message)
        {
            try
            {
                await _jsRuntime.InvokeVoidAsync("showToast", type, title, message);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Erreur lors de l'affichage de la notification: {ex.Message}");
            }
        }
    }
}
