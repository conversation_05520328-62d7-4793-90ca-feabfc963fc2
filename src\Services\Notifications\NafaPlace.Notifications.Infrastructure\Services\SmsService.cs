using Microsoft.Extensions.Logging;
using NafaPlace.Notifications.Application.Services;

namespace NafaPlace.Notifications.Infrastructure.Services;

public class SmsService : ISmsService
{
    private readonly ILogger<SmsService> _logger;

    public SmsService(ILogger<SmsService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> SendSmsAsync(string phoneNumber, string message)
    {
        try
        {
            // Simulation d'envoi de SMS pour les tests
            _logger.LogInformation("📱 Sending SMS to {PhoneNumber}: {Message}", phoneNumber, message);
            
            // Ici, vous pourriez intégrer avec Twilio, Orange Money SMS API, etc.
            await Task.Delay(100); // Simuler un délai réseau
            
            _logger.LogInformation("✅ SMS sent successfully to {PhoneNumber}", phoneNumber);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send SMS to {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    public async Task<bool> SendSmsWithTemplateAsync(string phoneNumber, string templateCode, Dictionary<string, object> variables)
    {
        try
        {
            _logger.LogInformation("📱 Sending templated SMS to {PhoneNumber} with template {TemplateCode}", phoneNumber, templateCode);
            
            // Simulation d'envoi avec template
            await Task.Delay(100);
            
            _logger.LogInformation("✅ Templated SMS sent successfully to {PhoneNumber}", phoneNumber);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send templated SMS to {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    public async Task<bool> SendBulkSmsAsync(List<string> phoneNumbers, string message)
    {
        var successCount = 0;
        
        foreach (var phoneNumber in phoneNumbers)
        {
            if (await SendSmsAsync(phoneNumber, message))
            {
                successCount++;
            }
        }

        _logger.LogInformation("📱 Bulk SMS sent: {SuccessCount}/{TotalCount}", successCount, phoneNumbers.Count);
        return successCount == phoneNumbers.Count;
    }

    public async Task<bool> IsPhoneNumberValidAsync(string phoneNumber)
    {
        await Task.CompletedTask;
        
        // Validation basique de numéro de téléphone guinéen
        if (string.IsNullOrWhiteSpace(phoneNumber))
            return false;

        // Format guinéen : +224XXXXXXXX ou 224XXXXXXXX ou 6XXXXXXXX/7XXXXXXXX
        var cleanNumber = phoneNumber.Replace("+", "").Replace(" ", "").Replace("-", "");
        
        if (cleanNumber.StartsWith("224") && cleanNumber.Length == 12)
            return true;
        
        if ((cleanNumber.StartsWith("6") || cleanNumber.StartsWith("7")) && cleanNumber.Length == 9)
            return true;

        return false;
    }
}
