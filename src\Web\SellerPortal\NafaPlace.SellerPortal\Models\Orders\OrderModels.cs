using System.ComponentModel.DataAnnotations;

namespace NafaPlace.SellerPortal.Models.Orders;

public class Order
{
    public int Id { get; set; }
    public string OrderNumber { get; set; } = "";
    public string CustomerName { get; set; } = "";
    public string CustomerEmail { get; set; } = "";
    public string CustomerPhone { get; set; } = "";
    public DateTime OrderDate { get; set; } = DateTime.Now;
    public string ShippingAddress { get; set; } = "";
    public string ShippingCity { get; set; } = "";
    public string ShippingMethod { get; set; } = "";
    public decimal ShippingFee { get; set; }
    public string PaymentMethod { get; set; } = "";
    public string PaymentStatus { get; set; } = "";
    public string Status { get; set; } = "";
    public decimal Subtotal { get; set; }
    public decimal TotalAmount { get; set; }
    public string Notes { get; set; } = "";
    public List<OrderItem> Items { get; set; } = new List<OrderItem>();
    public int SellerId { get; set; }
    public string SellerName { get; set; } = "";
}

public class OrderItem
{
    public int Id { get; set; }
    public int ProductId { get; set; }
    public string ProductName { get; set; } = "";
    public string ProductImageUrl { get; set; } = "";
    public decimal UnitPrice { get; set; }
    public int Quantity { get; set; }
    public decimal TotalPrice => UnitPrice * Quantity;
}

public class OrdersPagedResponse
{
    public List<Order> Orders { get; set; } = new List<Order>();
    public int TotalCount { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public int TotalPages => (int)Math.Ceiling(TotalCount / (double)PageSize);
    public bool HasPreviousPage => PageNumber > 1;
    public bool HasNextPage => PageNumber < TotalPages;
}

public class OrderFilterRequest
{
    public string? SearchTerm { get; set; }
    public string? Status { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 10;
    public int? SellerId { get; set; }
}

public class UpdateOrderStatusRequest
{
    [Required]
    public int OrderId { get; set; }
    
    [Required]
    public string Status { get; set; } = "";
    
    public string? Notes { get; set; }
}

public class UpdatePaymentStatusRequest
{
    [Required]
    public int OrderId { get; set; }
    
    [Required]
    public string PaymentStatus { get; set; } = "";
}

public class OrderStatusOptions
{
    public static readonly List<string> AllStatuses = new()
    {
        "En attente",
        "Confirmé", 
        "En préparation",
        "Expédié",
        "Livré",
        "Annulé"
    };
}

public class PaymentStatusOptions
{
    public static readonly List<string> AllStatuses = new()
    {
        "En attente",
        "Payé",
        "Remboursé",
        "Échoué"
    };
}
