using Microsoft.Extensions.Logging;
using NafaPlace.Notifications.Application.Services;

namespace NafaPlace.Notifications.Infrastructure.Services;

public class EmailService : IEmailService
{
    private readonly ILogger<EmailService> _logger;

    public EmailService(ILogger<EmailService> logger)
    {
        _logger = logger;
    }

    public async Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true)
    {
        try
        {
            // Simulation d'envoi d'email pour les tests
            _logger.LogInformation("📧 Sending email to {To}: {Subject}", to, subject);
            
            // Ici, vous pourriez intégrer avec SendGrid, SMTP, etc.
            await Task.Delay(100); // Simuler un délai réseau
            
            _logger.LogInformation("✅ Email sent successfully to {To}", to);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send email to {To}", to);
            return false;
        }
    }

    public async Task<bool> SendEmailWithTemplateAsync(string to, string templateCode, Dictionary<string, object> variables)
    {
        try
        {
            _logger.LogInformation("📧 Sending templated email to {To} with template {TemplateCode}", to, templateCode);
            
            // Simulation d'envoi avec template
            await Task.Delay(100);
            
            _logger.LogInformation("✅ Templated email sent successfully to {To}", to);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to send templated email to {To}", to);
            return false;
        }
    }

    public async Task<bool> SendBulkEmailAsync(List<string> recipients, string subject, string body, bool isHtml = true)
    {
        var successCount = 0;
        
        foreach (var recipient in recipients)
        {
            if (await SendEmailAsync(recipient, subject, body, isHtml))
            {
                successCount++;
            }
        }

        _logger.LogInformation("📧 Bulk email sent: {SuccessCount}/{TotalCount}", successCount, recipients.Count);
        return successCount == recipients.Count;
    }

    public async Task<bool> IsEmailValidAsync(string email)
    {
        await Task.CompletedTask;
        
        // Validation basique d'email
        if (string.IsNullOrWhiteSpace(email))
            return false;

        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
