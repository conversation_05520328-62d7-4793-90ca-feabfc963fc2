@page "/payment/success"
@using NafaPlace.Web.Models.Order
@inject NavigationManager NavigationManager
@inject IOrderService OrderService

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-lg border-success">
                <div class="card-header bg-success text-white text-center">
                    <h3><i class="fas fa-check-circle me-2"></i>Paiement Réussi !</h3>
                </div>
                <div class="card-body text-center">
                    @if (_loading)
                    {
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                        <p class="mt-2">Vérification du paiement...</p>
                    }
                    else
                    {
                        <div class="mb-4">
                            <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        </div>
                        
                        <h4 class="text-success mb-3">Votre paiement a été traité avec succès !</h4>
                        
                        @if (!string.IsNullOrEmpty(_orderId))
                        {
                            <div class="alert alert-success">
                                <strong>Numéro de commande :</strong> #@_orderId.PadLeft(8, '0')
                            </div>
                        }
                        
                        <p class="mb-4">
                            Merci pour votre achat ! Vous recevrez un email de confirmation sous peu.
                            Votre commande est maintenant en cours de traitement.
                        </p>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary btn-lg" @onclick="ViewOrder">
                                <i class="fas fa-eye me-2"></i>
                                Voir ma commande
                            </button>
                            <button class="btn btn-outline-secondary" @onclick="ContinueShopping">
                                <i class="fas fa-shopping-bag me-2"></i>
                                Continuer mes achats
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool _loading = true;
    private string _orderId = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Récupérer l'orderId depuis les paramètres de l'URL
            var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
            var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
            
            var orderIdValue = query["orderId"];
            if (!string.IsNullOrEmpty(orderIdValue))
            {
                _orderId = orderIdValue;
                
                // Mettre à jour le statut de la commande à "Completed"
                if (int.TryParse(_orderId, out int orderIdInt))
                {
                    await OrderService.UpdatePaymentStatusAsync(orderIdInt, "Completed", $"stripe_success_{DateTime.Now.Ticks}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du traitement du succès de paiement: {ex.Message}");
        }
        finally
        {
            _loading = false;
        }
    }

    private void ViewOrder()
    {
        if (!string.IsNullOrEmpty(_orderId))
        {
            NavigationManager.NavigateTo($"/order-confirmation/{_orderId}");
        }
        else
        {
            NavigationManager.NavigateTo("/orders");
        }
    }

    private void ContinueShopping()
    {
        NavigationManager.NavigateTo("/");
    }
}
