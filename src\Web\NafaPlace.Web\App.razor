<CascadingAuthenticationState>
    <Router AppAssembly="@typeof(App).Assembly">
        <Found Context="routeData">
            <RouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)" />
            <FocusOnNavigate RouteData="@routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>Page non trouvée</PageTitle>
            <LayoutView Layout="@typeof(MainLayout)">
                <div class="container text-center py-5">
                    <h1 class="display-1">404</h1>
                    <p class="lead">Désolé, la page que vous recherchez n'existe pas.</p>
                    <a href="/" class="btn btn-primary">
                        <i class="bi bi-house"></i> Retour à l'accueil
                    </a>
                </div>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>
