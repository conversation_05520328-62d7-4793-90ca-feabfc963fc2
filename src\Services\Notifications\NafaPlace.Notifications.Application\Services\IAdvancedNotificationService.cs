using NafaPlace.Notifications.Application.DTOs;
using NafaPlace.Notifications.Domain.Models;

namespace NafaPlace.Notifications.Application.Services;

public interface IAdvancedNotificationService
{
    // Enhanced notification sending
    Task<NotificationDto> SendNotificationAsync(SendNotificationRequest request);
    Task<NotificationDto> SendTemplatedNotificationAsync(SendTemplatedNotificationRequest request);
    Task<List<NotificationDto>> SendBulkNotificationAsync(BulkNotificationRequest request);
    Task<bool> SendEmailAsync(string to, string subject, string body, string? templateCode = null, Dictionary<string, object>? variables = null);
    Task<bool> SendSmsAsync(string phoneNumber, string message, string? templateCode = null, Dictionary<string, object>? variables = null);

    // Template management
    Task<NotificationTemplateDto> CreateTemplateAsync(CreateNotificationTemplateRequest request);
    Task<NotificationTemplateDto> UpdateTemplateAsync(int id, UpdateTemplateRequest request);
    Task<bool> DeleteTemplateAsync(int id);
    Task<NotificationTemplateDto?> GetTemplateAsync(int id);
    Task<NotificationTemplateDto?> GetTemplateByCodeAsync(string code);
    Task<List<NotificationTemplateDto>> GetTemplatesAsync(NotificationType? type = null, NotificationChannel? channel = null);

    // Preference management
    Task<List<NotificationPreferenceDto>> GetUserPreferencesAsync(string userId);
    Task<NotificationPreferenceDto> UpdatePreferenceAsync(string userId, UpdatePreferenceRequest request);
    Task<bool> CanSendNotificationAsync(string userId, NotificationType type, NotificationChannel channel);

    // Notification management
    Task<NotificationDto?> GetNotificationAsync(int id);
    Task<NotificationsPagedResult> GetUserNotificationsAsync(string userId, int page = 1, int pageSize = 20, bool? isRead = null);
    Task<bool> MarkAsReadAsync(int notificationId);
    Task<bool> MarkAllAsReadAsync(string userId);
    Task<bool> DeleteNotificationAsync(int notificationId);

    // Queue management
    Task<bool> ProcessPendingNotificationsAsync();
    Task<bool> RetryFailedNotificationsAsync();
    Task<int> GetQueueSizeAsync();

    // Statistics and analytics
    Task<NotificationStatsDto> GetNotificationStatsAsync(string? userId = null, DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<NotificationChannel, bool>> GetChannelHealthAsync();

    // Maintenance
    Task<int> CleanupOldNotificationsAsync(int daysToKeep = 30);
    Task<int> CleanupExpiredNotificationsAsync();
}

public interface IEmailService
{
    Task<bool> SendEmailAsync(string to, string subject, string body, bool isHtml = true);
    Task<bool> SendEmailWithTemplateAsync(string to, string templateCode, Dictionary<string, object> variables);
    Task<bool> SendBulkEmailAsync(List<string> recipients, string subject, string body, bool isHtml = true);
    Task<bool> IsEmailValidAsync(string email);
}

public interface ISmsService
{
    Task<bool> SendSmsAsync(string phoneNumber, string message);
    Task<bool> SendSmsWithTemplateAsync(string phoneNumber, string templateCode, Dictionary<string, object> variables);
    Task<bool> SendBulkSmsAsync(List<string> phoneNumbers, string message);
    Task<bool> IsPhoneNumberValidAsync(string phoneNumber);
}

public interface ITemplateEngine
{
    string ProcessTemplate(string template, Dictionary<string, object> variables);
    bool ValidateTemplate(string template);
    List<string> ExtractVariables(string template);
}

public class UpdateTemplateRequest
{
    public string? Name { get; set; }
    public string? Subject { get; set; }
    public string? Body { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
    public List<string>? Variables { get; set; }
}

public class CreateNotificationTemplateRequest
{
    public string Name { get; set; } = string.Empty;
    public string Code { get; set; } = string.Empty;
    public NotificationType Type { get; set; }
    public NotificationChannel Channel { get; set; }
    public string Subject { get; set; } = string.Empty;
    public string Body { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
    public string Language { get; set; } = "fr";
    public List<string> Variables { get; set; } = new();
}
