using Microsoft.EntityFrameworkCore;
using NafaPlace.Notifications.Application.Interfaces;
using NafaPlace.Notifications.Domain.Models;

namespace NafaPlace.Notifications.Infrastructure.Data;

public class NotificationsDbContext : DbContext, INotificationsDbContext
{
    public NotificationsDbContext(DbContextOptions<NotificationsDbContext> options) : base(options)
    {
    }

    public DbSet<Notification> Notifications { get; set; }
    public DbSet<NotificationTemplate> NotificationTemplates { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Notification configuration
        modelBuilder.Entity<Notification>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.UserId).IsRequired().HasMaxLength(50);
            entity.Property(e => e.Title).IsRequired().HasMaxLength(200);
            entity.Property(e => e.Message).IsRequired().HasMaxLength(1000);
            entity.Property(e => e.Type).IsRequired();
            entity.Property(e => e.Priority).IsRequired();
            entity.Property(e => e.CreatedAt).IsRequired();
            entity.Property(e => e.ActionUrl).HasMaxLength(500);
            entity.Property(e => e.ImageUrl).HasMaxLength(500);

            // Index for better query performance
            entity.HasIndex(e => e.UserId);
            entity.HasIndex(e => e.CreatedAt);
            entity.HasIndex(e => e.IsRead);
            entity.HasIndex(e => new { e.UserId, e.IsRead });
        });

        // NotificationTemplate configuration
        modelBuilder.Entity<NotificationTemplate>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(100);
            entity.Property(e => e.TitleTemplate).IsRequired().HasMaxLength(200);
            entity.Property(e => e.MessageTemplate).IsRequired().HasMaxLength(1000);
            entity.Property(e => e.Type).IsRequired();
            entity.Property(e => e.Priority).IsRequired();
            entity.Property(e => e.CreatedAt).IsRequired();
            entity.Property(e => e.UpdatedAt).IsRequired();

            // Unique constraint on name
            entity.HasIndex(e => e.Name).IsUnique();
        });

        // Seed data
        modelBuilder.Entity<NotificationTemplate>().HasData(
            new NotificationTemplate
            {
                Id = 1,
                Name = "OrderStatusChange",
                TitleTemplate = "Mise à jour de votre commande #{OrderId}",
                MessageTemplate = "Le statut de votre commande #{OrderId} a été mis à jour vers: {Status}",
                Type = NotificationType.OrderUpdate,
                Priority = NotificationPriority.Normal,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new NotificationTemplate
            {
                Id = 2,
                Name = "PaymentConfirmation",
                TitleTemplate = "Paiement confirmé",
                MessageTemplate = "Votre paiement de {Amount} GNF pour la commande #{OrderId} a été confirmé avec succès.",
                Type = NotificationType.PaymentUpdate,
                Priority = NotificationPriority.High,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new NotificationTemplate
            {
                Id = 3,
                Name = "NewReview",
                TitleTemplate = "Nouvel avis sur {ProductName}",
                MessageTemplate = "Un client a laissé un avis {Rating}/5 étoiles sur votre produit {ProductName}.",
                Type = NotificationType.ReviewUpdate,
                Priority = NotificationPriority.Normal,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new NotificationTemplate
            {
                Id = 4,
                Name = "LowStock",
                TitleTemplate = "Stock faible: {ProductName}",
                MessageTemplate = "Attention! Il ne reste que {CurrentStock} unités de {ProductName} en stock.",
                Type = NotificationType.ProductUpdate,
                Priority = NotificationPriority.High,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        );
    }
}
