using NafaPlace.Inventory.Application.DTOs;
using NafaPlace.Inventory.Domain.Models;

namespace NafaPlace.Inventory.Infrastructure.Repositories;

public interface IInventoryRepository
{
    // Stock Reservations
    Task<StockReservation> CreateReservationAsync(StockReservation reservation);
    Task<bool> UpdateReservationAsync(StockReservation reservation);
    Task<StockReservation?> GetReservationAsync(int reservationId);
    Task<List<StockReservation>> GetUserReservationsAsync(string userId);
    Task<List<StockReservation>> GetProductReservationsAsync(int productId);
    Task<List<StockReservation>> GetExpiredReservationsAsync();
    Task<int> GetReservedStockAsync(int productId);
    Task<int> GetActiveReservationsCountAsync(int? sellerId = null);
    Task<int> CleanupExpiredReservationsAsync();

    // Stock Alerts
    Task<StockAlert> CreateAlertAsync(StockAlert alert);
    Task<bool> UpdateAlertAsync(StockAlert alert);
    Task<StockAlert?> GetAlertAsync(int alertId);
    Task<List<StockAlert>> GetActiveAlertsAsync(int? sellerId = null);
    Task<List<StockAlert>> GetSellerAlertsAsync(int sellerId);
    Task<List<StockAlert>> GetPendingAlertsAsync();
    Task<int> GetPendingAlertsCountAsync(int? sellerId = null);

    // Stock Movements
    Task<StockMovement> CreateMovementAsync(StockMovement movement);
    Task<List<StockMovement>> GetProductMovementsAsync(int productId, int page = 1, int pageSize = 20);
    Task<List<StockMovement>> GetSellerMovementsAsync(int sellerId, int page = 1, int pageSize = 20);
    Task<int> CleanupOldMovementsAsync(int daysToKeep = 90);

    // Analytics
    Task<List<TopProductDto>> GetTopSellingProductsAsync(int? sellerId = null, int count = 10);
    Task<List<TopProductDto>> GetLowStockProductsAsync(int? sellerId = null, int threshold = 10);
}

public interface IProductStockRepository
{
    Task<int> GetCurrentStockAsync(int productId);
    Task<bool> UpdateStockAsync(int productId, int newQuantity);
    Task<ProductInfo?> GetProductInfoAsync(int productId);
    Task<int> GetTotalProductsCountAsync(int? sellerId = null);
    Task<int> GetLowStockCountAsync(int? sellerId = null, int threshold = 10);
    Task<int> GetOutOfStockCountAsync(int? sellerId = null);
    Task<decimal> GetTotalInventoryValueAsync(int? sellerId = null);
    Task<bool> RecalculateStockLevelsAsync();
}

public class ProductInfo
{
    public int Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public int SellerId { get; set; }
    public string? SellerName { get; set; }
    public int CurrentStock { get; set; }
}
