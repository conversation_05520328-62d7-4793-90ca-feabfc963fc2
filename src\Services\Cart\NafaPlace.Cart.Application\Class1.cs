﻿using NafaPlace.Cart.Domain;

namespace NafaPlace.Cart.Application.DTOs;

public class ShoppingCartDto
{
    public string UserId { get; set; } = string.Empty;
    public List<CartItemDto> Items { get; set; } = new();
    public decimal SubTotal { get; set; }
    public decimal ShippingFee { get; set; }
    public decimal Tax { get; set; }
    public decimal Total { get; set; }
    public int ItemCount { get; set; }
    public string Currency { get; set; } = "GNF";
    public DateTime LastUpdated { get; set; }
}

public class CartItemDto
{
    public int ProductId { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public decimal UnitPrice { get; set; }
    public int Quantity { get; set; }
    public string ImageUrl { get; set; } = string.Empty;
    public string? ProductSku { get; set; }
    public int? SellerId { get; set; }
    public string? SellerName { get; set; }
    public decimal LineTotal => UnitPrice * Quantity;
    public bool IsAvailable { get; set; } = true;
    public int MaxQuantity { get; set; } = 999;
}

public class AddToCartRequest
{
    public int ProductId { get; set; }
    public int Quantity { get; set; } = 1;
}

public class UpdateCartItemRequest
{
    public int ProductId { get; set; }
    public int Quantity { get; set; }
}

public class CartSummaryDto
{
    public int ItemCount { get; set; }
    public decimal SubTotal { get; set; }
    public decimal Total { get; set; }
    public string Currency { get; set; } = "GNF";
}
