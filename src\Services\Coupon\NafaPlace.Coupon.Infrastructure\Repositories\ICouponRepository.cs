using NafaPlace.Coupon.Application.DTOs;
using NafaPlace.Coupon.Domain.Models;

namespace NafaPlace.Coupon.Infrastructure.Repositories;

public interface ICouponRepository
{
    // Basic CRUD operations
    Task<Domain.Models.Coupon> CreateAsync(Domain.Models.Coupon coupon);
    Task<Domain.Models.Coupon> UpdateAsync(Domain.Models.Coupon coupon);
    Task<bool> DeleteAsync(int id);
    Task<Domain.Models.Coupon?> GetByIdAsync(int id);
    Task<Domain.Models.Coupon?> GetByCodeAsync(string code);
    Task<List<Domain.Models.Coupon>> GetCouponsAsync(int page = 1, int pageSize = 20, bool? isActive = null);
    Task<List<Domain.Models.Coupon>> GetActiveCouponsAsync();
    Task<List<Domain.Models.Coupon>> GetExpiredCouponsAsync();

    // Usage tracking
    Task<bool> RecordUsageAsync(CouponUsage usage);
    Task<bool> IncrementUsageCountAsync(int couponId);
    Task<int> GetUserUsageCountAsync(int couponId, string userId);

    // Statistics
    Task<CouponStatsResult> GetCouponStatsAsync(int couponId);
    Task<List<CouponUsageStatsDto>> GetUsageStatsAsync(DateTime? startDate = null, DateTime? endDate = null);

    // Maintenance
    Task<int> CleanupExpiredCouponsAsync();
}

public class CouponStatsResult
{
    public int TotalUsages { get; set; }
    public decimal TotalDiscountGiven { get; set; }
    public int UniqueUsers { get; set; }
    public DateTime? LastUsed { get; set; }
}
