using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using NafaPlace.Catalog.Domain.Common;

namespace NafaPlace.Catalog.Domain.Models;

public class Product : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public required string Name { get; set; }

    [Required]
    [MaxLength(500)]
    public required string Description { get; set; }

    [Required]
    [Range(0, double.MaxValue)]
    public decimal Price { get; set; }

    public int CategoryId { get; set; }

    [Required]
    [Range(0, int.MaxValue)]
    public int StockQuantity { get; set; }

    [Required]
    [MaxLength(3)]
    public required string Currency { get; set; } = "GNF"; // Franc Guinéen par défaut

    [MaxLength(50)]
    public string? Brand { get; set; }

    [MaxLength(50)]
    public string? Model { get; set; }

    [Range(0, double.MaxValue)]
    public decimal Weight { get; set; } // En kilogrammes

    [MaxLength(50)]
    public string? Dimensions { get; set; } // Format : "LxWxH" en cm

    [Range(0, 5)]
    public decimal Rating { get; set; }

    public bool IsActive { get; set; } = true;
    public bool IsFeatured { get; set; }

    [Required]
    public int SellerId { get; set; }

    public Category? Category { get; set; }
    public Seller? Seller { get; set; }
    public List<ProductImage> Images { get; set; } = new();
    public List<ProductVariant> Variants { get; set; } = new();
    public List<ProductAttribute> Attributes { get; set; } = new();

    public new DateTime CreatedAt { get; set; }
    public new DateTime UpdatedAt { get; set; }
}
