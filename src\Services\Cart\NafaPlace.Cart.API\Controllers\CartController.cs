using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NafaPlace.Cart.Application;
using NafaPlace.Cart.Application.DTOs;
using NafaPlace.Cart.Application.Services;
using NafaPlace.Cart.Domain;
using System.Security.Claims;

namespace NafaPlace.Cart.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class CartController : ControllerBase
    {
        private readonly IShoppingCartRepository _repository;
        private readonly ICartService _cartService;

        public CartController(IShoppingCartRepository repository, ICartService cartService)
        {
            _repository = repository;
            _cartService = cartService;
        }

        [HttpGet("{userId}")]
        public async Task<ActionResult<ShoppingCartDto>> GetCart(string userId)
        {
            try
            {
                var cart = await _cartService.GetCartAsync(userId);
                return Ok(cart);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpGet("{userId}/summary")]
        public async Task<ActionResult<CartSummaryDto>> GetCartSummary(string userId)
        {
            try
            {
                var summary = await _cartService.GetCartSummaryAsync(userId);
                return Ok(summary);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost("{userId}/items")]
        public async Task<ActionResult<ShoppingCartDto>> AddItemToCart(string userId, [FromBody] AddToCartRequest request)
        {
            try
            {
                var cart = await _cartService.AddItemToCartAsync(userId, request);
                return Ok(cart);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPut("{userId}/items")]
        public async Task<ActionResult<ShoppingCartDto>> UpdateItemQuantity(string userId, [FromBody] UpdateCartItemRequest request)
        {
            try
            {
                var cart = await _cartService.UpdateItemQuantityAsync(userId, request);
                return Ok(cart);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpDelete("{userId}/items/{productId}")]
        public async Task<ActionResult<ShoppingCartDto>> RemoveItemFromCart(string userId, int productId)
        {
            try
            {
                var cart = await _cartService.RemoveItemFromCartAsync(userId, productId);
                return Ok(cart);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpDelete("{userId}")]
        public async Task<IActionResult> ClearCart(string userId)
        {
            try
            {
                await _cartService.ClearCartAsync(userId);
                return Ok(new { message = "Cart cleared successfully" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost("{userId}/validate")]
        public async Task<ActionResult<bool>> ValidateCart(string userId)
        {
            try
            {
                var isValid = await _cartService.ValidateCartAsync(userId);
                return Ok(new { isValid, message = isValid ? "Cart is valid" : "Some items were removed or updated" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost("{userId}/coupon")]
        public async Task<ActionResult<ShoppingCartDto>> ApplyCoupon(string userId, [FromBody] string couponCode)
        {
            try
            {
                var cart = await _cartService.ApplyCouponAsync(userId, couponCode);
                return Ok(cart);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpDelete("{userId}/coupon")]
        public async Task<ActionResult<ShoppingCartDto>> RemoveCoupon(string userId)
        {
            try
            {
                var cart = await _cartService.RemoveCouponAsync(userId);
                return Ok(cart);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        [HttpPost("{userId}/shipping")]
        public async Task<ActionResult<decimal>> CalculateShipping(string userId, [FromBody] string shippingAddress)
        {
            try
            {
                var shippingCost = await _cartService.CalculateShippingAsync(userId, shippingAddress);
                return Ok(new { shippingCost, currency = "GNF" });
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Internal server error: {ex.Message}");
            }
        }

        // Legacy endpoints for backward compatibility
        [HttpPost]
        public async Task<ActionResult<ShoppingCart>> UpdateCart(ShoppingCart cart)
        {
            return Ok(await _repository.UpdateCartAsync(cart));
        }

        [HttpPost("{userId}/items/legacy")]
        public async Task<ActionResult<ShoppingCart>> AddItemToCartLegacy(string userId, CartItem item)
        {
            return Ok(await _repository.AddItemToCartAsync(userId, item));
        }
    }
}