using Microsoft.Extensions.Logging;
using NafaPlace.Notifications.Application.DTOs;
using NafaPlace.Notifications.Domain.Models;
using NafaPlace.Notifications.Infrastructure.Repositories;

namespace NafaPlace.Notifications.Application.Services;

public class AdvancedNotificationService : IAdvancedNotificationService
{
    private readonly INotificationRepository _repository;
    private readonly IEmailService _emailService;
    private readonly ISmsService _smsService;
    private readonly ITemplateEngine _templateEngine;
    private readonly ILogger<AdvancedNotificationService> _logger;

    public AdvancedNotificationService(
        INotificationRepository repository,
        IEmailService emailService,
        ISmsService smsService,
        ITemplateEngine templateEngine,
        ILogger<AdvancedNotificationService> logger)
    {
        _repository = repository;
        _emailService = emailService;
        _smsService = smsService;
        _templateEngine = templateEngine;
        _logger = logger;
    }

    public async Task<NotificationDto> SendNotificationAsync(SendNotificationRequest request)
    {
        // Vérifier les préférences utilisateur
        var canSend = await CanSendNotificationAsync(request.UserId, request.Type, request.Channel);
        if (!canSend)
        {
            throw new InvalidOperationException("L'utilisateur a désactivé ce type de notification");
        }

        var notification = new NotificationLog
        {
            UserId = request.UserId,
            UserName = request.UserName,
            Recipient = request.Recipient,
            Channel = request.Channel,
            Type = request.Type,
            Subject = request.Subject,
            Content = request.Content,
            Priority = request.Priority,
            ScheduledAt = request.ScheduledAt,
            Reference = request.Reference,
            TemplateCode = request.TemplateCode,
            Status = request.ScheduledAt.HasValue && request.ScheduledAt > DateTime.UtcNow 
                ? NotificationStatus.Pending 
                : NotificationStatus.Pending
        };

        var createdNotification = await _repository.CreateNotificationAsync(notification);

        // Si pas de planification, envoyer immédiatement
        if (!request.ScheduledAt.HasValue || request.ScheduledAt <= DateTime.UtcNow)
        {
            await ProcessNotificationAsync(createdNotification);
        }

        return MapToDto(createdNotification);
    }

    public async Task<NotificationDto> SendTemplatedNotificationAsync(SendTemplatedNotificationRequest request)
    {
        var template = await _repository.GetTemplateByCodeAsync(request.TemplateCode);
        if (template == null)
        {
            throw new ArgumentException($"Template '{request.TemplateCode}' not found");
        }

        if (!template.IsActive)
        {
            throw new InvalidOperationException($"Template '{request.TemplateCode}' is not active");
        }

        // Traiter le template avec les variables
        var subject = _templateEngine.ProcessTemplate(template.Subject, request.Variables);
        var content = _templateEngine.ProcessTemplate(template.Body, request.Variables);

        var sendRequest = new SendNotificationRequest
        {
            UserId = request.UserId,
            UserName = request.UserName,
            Recipient = request.Recipient,
            Channel = request.Channel,
            Type = template.Type,
            Subject = subject,
            Content = content,
            Priority = request.Priority,
            ScheduledAt = request.ScheduledAt,
            Reference = request.Reference,
            TemplateCode = request.TemplateCode
        };

        return await SendNotificationAsync(sendRequest);
    }

    public async Task<List<NotificationDto>> SendBulkNotificationAsync(BulkNotificationRequest request)
    {
        var notifications = new List<NotificationDto>();

        for (int i = 0; i < request.UserIds.Count && i < request.Recipients.Count; i++)
        {
            try
            {
                var sendRequest = new SendTemplatedNotificationRequest
                {
                    UserId = request.UserIds[i],
                    UserName = request.UserIds[i],
                    Recipient = request.Recipients[i],
                    Channel = request.Channel,
                    TemplateCode = request.TemplateCode,
                    Variables = request.Variables,
                    Priority = request.Priority,
                    ScheduledAt = request.ScheduledAt,
                    Reference = request.Reference
                };

                var notification = await SendTemplatedNotificationAsync(sendRequest);
                notifications.Add(notification);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send bulk notification to user {UserId}", request.UserIds[i]);
            }
        }

        return notifications;
    }

    public async Task<bool> SendEmailAsync(string to, string subject, string body, string? templateCode = null, Dictionary<string, object>? variables = null)
    {
        try
        {
            if (!string.IsNullOrEmpty(templateCode) && variables != null)
            {
                return await _emailService.SendEmailWithTemplateAsync(to, templateCode, variables);
            }
            else
            {
                return await _emailService.SendEmailAsync(to, subject, body, true);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Recipient}", to);
            return false;
        }
    }

    public async Task<bool> SendSmsAsync(string phoneNumber, string message, string? templateCode = null, Dictionary<string, object>? variables = null)
    {
        try
        {
            if (!string.IsNullOrEmpty(templateCode) && variables != null)
            {
                return await _smsService.SendSmsWithTemplateAsync(phoneNumber, templateCode, variables);
            }
            else
            {
                return await _smsService.SendSmsAsync(phoneNumber, message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send SMS to {PhoneNumber}", phoneNumber);
            return false;
        }
    }

    // Méthodes de gestion des templates
    public async Task<NotificationTemplateDto> CreateTemplateAsync(CreateNotificationTemplateRequest request)
    {
        var template = new NotificationTemplate
        {
            Name = request.Name,
            Code = request.Code,
            Type = request.Type,
            Channel = request.Channel,
            Subject = request.Subject,
            Body = request.Body,
            Description = request.Description,
            IsActive = request.IsActive,
            Language = request.Language,
            Variables = string.Join(",", request.Variables)
        };

        var createdTemplate = await _repository.CreateTemplateAsync(template);
        return MapTemplateToDto(createdTemplate);
    }

    public async Task<NotificationTemplateDto> UpdateTemplateAsync(int id, UpdateTemplateRequest request)
    {
        var template = await _repository.GetTemplateAsync(id);
        if (template == null)
        {
            throw new ArgumentException("Template not found");
        }

        if (!string.IsNullOrEmpty(request.Name))
            template.Name = request.Name;
        
        if (!string.IsNullOrEmpty(request.Subject))
            template.Subject = request.Subject;
        
        if (!string.IsNullOrEmpty(request.Body))
            template.Body = request.Body;
        
        if (request.Description != null)
            template.Description = request.Description;
        
        if (request.IsActive.HasValue)
            template.IsActive = request.IsActive.Value;
        
        if (request.Variables != null)
            template.Variables = string.Join(",", request.Variables);

        var updatedTemplate = await _repository.UpdateTemplateAsync(template);
        return MapTemplateToDto(updatedTemplate);
    }

    public async Task<bool> DeleteTemplateAsync(int id)
    {
        return await _repository.DeleteTemplateAsync(id);
    }

    public async Task<NotificationTemplateDto?> GetTemplateAsync(int id)
    {
        var template = await _repository.GetTemplateAsync(id);
        return template != null ? MapTemplateToDto(template) : null;
    }

    public async Task<NotificationTemplateDto?> GetTemplateByCodeAsync(string code)
    {
        var template = await _repository.GetTemplateByCodeAsync(code);
        return template != null ? MapTemplateToDto(template) : null;
    }

    public async Task<List<NotificationTemplateDto>> GetTemplatesAsync(NotificationType? type = null, NotificationChannel? channel = null)
    {
        var templates = await _repository.GetTemplatesAsync(type, channel);
        return templates.Select(MapTemplateToDto).ToList();
    }

    // Méthodes de gestion des préférences
    public async Task<List<NotificationPreferenceDto>> GetUserPreferencesAsync(string userId)
    {
        var preferences = await _repository.GetUserPreferencesAsync(userId);
        return preferences.Select(MapPreferenceToDto).ToList();
    }

    public async Task<NotificationPreferenceDto> UpdatePreferenceAsync(string userId, UpdatePreferenceRequest request)
    {
        var preference = await _repository.GetUserPreferenceAsync(userId, request.Type, request.Channel);
        
        if (preference == null)
        {
            preference = new NotificationPreference
            {
                UserId = userId,
                Type = request.Type,
                Channel = request.Channel,
                IsEnabled = request.IsEnabled,
                QuietHoursStart = request.QuietHoursStart,
                QuietHoursEnd = request.QuietHoursEnd,
                Frequency = request.Frequency
            };
            
            preference = await _repository.CreatePreferenceAsync(preference);
        }
        else
        {
            preference.IsEnabled = request.IsEnabled;
            preference.QuietHoursStart = request.QuietHoursStart;
            preference.QuietHoursEnd = request.QuietHoursEnd;
            preference.Frequency = request.Frequency;
            
            preference = await _repository.UpdatePreferenceAsync(preference);
        }

        return MapPreferenceToDto(preference);
    }

    public async Task<bool> CanSendNotificationAsync(string userId, NotificationType type, NotificationChannel channel)
    {
        var preference = await _repository.GetUserPreferenceAsync(userId, type, channel);
        
        if (preference == null)
        {
            // Par défaut, autoriser les notifications
            return true;
        }

        if (!preference.IsEnabled)
        {
            return false;
        }

        // Vérifier les heures de silence
        if (preference.QuietHoursStart.HasValue && preference.QuietHoursEnd.HasValue)
        {
            var now = DateTime.Now.TimeOfDay;
            var start = preference.QuietHoursStart.Value;
            var end = preference.QuietHoursEnd.Value;

            if (start <= end)
            {
                // Même jour
                if (now >= start && now <= end)
                {
                    return false;
                }
            }
            else
            {
                // Traverse minuit
                if (now >= start || now <= end)
                {
                    return false;
                }
            }
        }

        // Vérifier la fréquence
        if (preference.Frequency != NotificationFrequency.Immediate && preference.LastSent.HasValue)
        {
            var timeSinceLastSent = DateTime.UtcNow - preference.LastSent.Value;
            
            return preference.Frequency switch
            {
                NotificationFrequency.Hourly => timeSinceLastSent.TotalHours >= 1,
                NotificationFrequency.Daily => timeSinceLastSent.TotalDays >= 1,
                NotificationFrequency.Weekly => timeSinceLastSent.TotalDays >= 7,
                NotificationFrequency.Monthly => timeSinceLastSent.TotalDays >= 30,
                NotificationFrequency.Never => false,
                _ => true
            };
        }

        return true;
    }

    // Méthodes de gestion des notifications
    public async Task<NotificationDto?> GetNotificationAsync(int id)
    {
        var notification = await _repository.GetNotificationAsync(id);
        return notification != null ? MapToDto(notification) : null;
    }

    public async Task<NotificationsPagedResult> GetUserNotificationsAsync(string userId, int page = 1, int pageSize = 20, bool? isRead = null)
    {
        var notifications = await _repository.GetUserNotificationsAsync(userId, page, pageSize, isRead);
        var totalCount = await _repository.GetUserNotificationsCountAsync(userId, isRead);

        return new NotificationsPagedResult
        {
            Notifications = notifications.Select(MapToDto).ToList(),
            TotalCount = totalCount,
            Page = page,
            PageSize = pageSize,
            TotalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        };
    }

    public async Task<bool> MarkAsReadAsync(int notificationId)
    {
        var notification = await _repository.GetNotificationAsync(notificationId);
        if (notification == null) return false;

        notification.ReadAt = DateTime.UtcNow;
        notification.Status = NotificationStatus.Read;

        return await _repository.UpdateNotificationAsync(notification);
    }

    public async Task<bool> MarkAllAsReadAsync(string userId)
    {
        return await _repository.MarkAllAsReadAsync(userId);
    }

    public async Task<bool> DeleteNotificationAsync(int notificationId)
    {
        return await _repository.DeleteNotificationAsync(notificationId);
    }

    // Méthodes de gestion de la queue
    public async Task<bool> ProcessPendingNotificationsAsync()
    {
        var pendingNotifications = await _repository.GetPendingNotificationsAsync();
        var processedCount = 0;

        foreach (var notification in pendingNotifications)
        {
            try
            {
                await ProcessNotificationAsync(notification);
                processedCount++;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process notification {NotificationId}", notification.Id);
            }
        }

        _logger.LogInformation("Processed {Count} pending notifications", processedCount);
        return processedCount > 0;
    }

    public async Task<bool> RetryFailedNotificationsAsync()
    {
        var failedNotifications = await _repository.GetFailedNotificationsAsync();
        var retriedCount = 0;

        foreach (var notification in failedNotifications)
        {
            if (notification.RetryCount < notification.MaxRetries)
            {
                try
                {
                    notification.RetryCount++;
                    notification.Status = NotificationStatus.Pending;
                    await _repository.UpdateNotificationAsync(notification);

                    await ProcessNotificationAsync(notification);
                    retriedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to retry notification {NotificationId}", notification.Id);
                }
            }
        }

        _logger.LogInformation("Retried {Count} failed notifications", retriedCount);
        return retriedCount > 0;
    }

    public async Task<int> GetQueueSizeAsync()
    {
        return await _repository.GetPendingNotificationsCountAsync();
    }

    // Statistiques
    public async Task<NotificationStatsDto> GetNotificationStatsAsync(string? userId = null, DateTime? startDate = null, DateTime? endDate = null)
    {
        return await _repository.GetNotificationStatsAsync(userId, startDate, endDate);
    }

    public async Task<Dictionary<NotificationChannel, bool>> GetChannelHealthAsync()
    {
        var health = new Dictionary<NotificationChannel, bool>();

        // Vérifier la santé de chaque canal
        health[NotificationChannel.Email] = await _emailService.IsEmailValidAsync("<EMAIL>");
        health[NotificationChannel.SMS] = await _smsService.IsPhoneNumberValidAsync("+224000000000");
        health[NotificationChannel.InApp] = true; // Toujours disponible
        health[NotificationChannel.Push] = true; // Supposé disponible

        return health;
    }

    // Maintenance
    public async Task<int> CleanupOldNotificationsAsync(int daysToKeep = 30)
    {
        return await _repository.CleanupOldNotificationsAsync(daysToKeep);
    }

    public async Task<int> CleanupExpiredNotificationsAsync()
    {
        return await _repository.CleanupExpiredNotificationsAsync();
    }

    // Méthodes privées
    private async Task ProcessNotificationAsync(NotificationLog notification)
    {
        try
        {
            bool success = false;

            switch (notification.Channel)
            {
                case NotificationChannel.Email:
                    success = await _emailService.SendEmailAsync(notification.Recipient, notification.Subject, notification.Content, true);
                    break;

                case NotificationChannel.SMS:
                    success = await _smsService.SendSmsAsync(notification.Recipient, notification.Content);
                    break;

                case NotificationChannel.InApp:
                    // Les notifications in-app sont déjà créées en base
                    success = true;
                    break;

                case NotificationChannel.Push:
                    // Implémentation des notifications push
                    success = true; // Simulé pour l'instant
                    break;

                default:
                    _logger.LogWarning("Unsupported notification channel: {Channel}", notification.Channel);
                    break;
            }

            if (success)
            {
                notification.Status = NotificationStatus.Sent;
                notification.SentAt = DateTime.UtcNow;

                // Pour les notifications in-app, marquer comme livrées immédiatement
                if (notification.Channel == NotificationChannel.InApp)
                {
                    notification.Status = NotificationStatus.Delivered;
                    notification.DeliveredAt = DateTime.UtcNow;
                }
            }
            else
            {
                notification.Status = NotificationStatus.Failed;
                notification.ErrorMessage = "Failed to send notification";
            }

            await _repository.UpdateNotificationAsync(notification);
        }
        catch (Exception ex)
        {
            notification.Status = NotificationStatus.Failed;
            notification.ErrorMessage = ex.Message;
            await _repository.UpdateNotificationAsync(notification);
            throw;
        }
    }

    // Méthodes de mapping
    private static NotificationDto MapToDto(NotificationLog notification)
    {
        return new NotificationDto
        {
            Id = notification.Id,
            UserId = notification.UserId,
            UserName = notification.UserName,
            Title = notification.Subject,
            Message = notification.Content,
            Recipient = notification.Recipient,
            Channel = notification.Channel,
            Type = notification.Type,
            IsRead = notification.ReadAt.HasValue,
            Status = notification.Status,
            CreatedAt = notification.CreatedAt,
            ReadAt = notification.ReadAt,
            SentAt = notification.SentAt,
            DeliveredAt = notification.DeliveredAt,
            Priority = notification.Priority,
            ErrorMessage = notification.ErrorMessage,
            RetryCount = notification.RetryCount,
            Reference = notification.Reference,
            TemplateCode = notification.TemplateCode
        };
    }

    private static NotificationTemplateDto MapTemplateToDto(NotificationTemplate template)
    {
        return new NotificationTemplateDto
        {
            Id = template.Id,
            Name = template.Name,
            Code = template.Code,
            Type = template.Type,
            Channel = template.Channel,
            Subject = template.Subject,
            Body = template.Body,
            Description = template.Description,
            IsActive = template.IsActive,
            Language = template.Language,
            Variables = !string.IsNullOrEmpty(template.Variables)
                ? template.Variables.Split(',').ToList()
                : new List<string>(),
            CreatedAt = template.CreatedAt
        };
    }

    private static NotificationPreferenceDto MapPreferenceToDto(NotificationPreference preference)
    {
        return new NotificationPreferenceDto
        {
            Id = preference.Id,
            UserId = preference.UserId,
            Type = preference.Type,
            Channel = preference.Channel,
            IsEnabled = preference.IsEnabled,
            QuietHoursStart = preference.QuietHoursStart,
            QuietHoursEnd = preference.QuietHoursEnd,
            TimeZone = preference.TimeZone,
            Language = preference.Language,
            Frequency = preference.Frequency,
            LastSent = preference.LastSent
        };
    }
}
